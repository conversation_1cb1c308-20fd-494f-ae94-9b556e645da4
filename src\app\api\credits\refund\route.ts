import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const userId = (session?.user as any)?.id
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { credits, description } = await request.json()

    if (!credits || credits <= 0) {
      return NextResponse.json({ error: 'Invalid credit amount' }, { status: 400 })
    }

    // Add credits back to user account
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        credits: {
          increment: credits
        }
      }
    })

    // Record the refund transaction
    await prisma.transaction.create({
      data: {
        userId: userId,
        type: 'REFUND',
        amount: credits,
        description: description || 'Credit refund',
        status: 'completed'
      }
    })

    const newBalance = updatedUser.credits

    return NextResponse.json({
      success: true,
      message: 'Credits refunded successfully',
      newBalance,
      refundedAmount: credits
    })

  } catch (error: unknown) {
    console.error('Credit refund error:', error)
    return NextResponse.json(
      { error: 'Failed to refund credits' },
      { status: 500 }
    )
  }
}
