import { ProtectedRoute } from '@/components/auth/protected-route'
import type { Metadata } from 'next'
import { HomePageClient } from '@/components/home-page-client'
import { generateMetadata } from '@/lib/seo-config'

// Homepage uses default SEO from seo-config.ts
export const metadata: Metadata = generateMetadata()

export default function Home() {
  return (
    <ProtectedRoute requireAuth={false}>
      <HomePageClient />
    </ProtectedRoute>
  )
}
