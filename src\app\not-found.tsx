'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft, Clock, Lightbulb } from 'lucide-react'
import Link from 'next/link'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      <div className="max-w-4xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="outline" className="flex items-center space-x-2">
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Home</span>
            </Button>
          </Link>
        </div>

        {/* Coming Soon Content */}
        <div className="text-center">
          <div className="mx-auto w-24 h-24 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center mb-8">
            <Lightbulb className="w-12 h-12 text-purple-600" />
          </div>
          
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Coming Soon
          </h1>
          
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            This feature is currently under development. We&apos;re working hard to bring you amazing new capabilities!
          </p>

          <div className="bg-white rounded-lg shadow-sm border p-8 max-w-md mx-auto">
            <Clock className="w-16 h-16 text-purple-500 mx-auto mb-4" />
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              Stay Tuned
            </h2>
            <p className="text-gray-600 mb-6">
              We&apos;re constantly improving DocuChampAI to provide you with the best document analysis experience.
            </p>
            <div className="text-sm text-purple-600 font-medium">
              Expected: Soon
            </div>
          </div>

          {/* Available Now */}
          <div className="mt-12">
            <p className="text-gray-600 mb-4">
              In the meantime, try our available feature:
            </p>
            <Link href="/features/document-analysis">
              <Button className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3">
                Document Analysis
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
