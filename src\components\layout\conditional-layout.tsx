'use client'

import { useSession } from 'next-auth/react'
import { Sidebar } from './sidebar'

interface ConditionalLayoutProps {
  children: React.ReactNode
}

export function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const { data: session, status } = useSession()

  // Show loading state while checking authentication
  if (status === 'loading') {
    return (
      <div className="flex flex-1">
        <main className="flex-1 transition-all duration-300">
          {children}
        </main>
      </div>
    )
  }

  // If user is authenticated, show layout with sidebar
  if (session) {
    return (
      <div className="flex flex-1">
        <Sidebar />
        <main className="flex-1 transition-all duration-300 min-w-0">
          {children}
        </main>
      </div>
    )
  }

  // If user is not authenticated, show layout without sidebar
  return (
    <div className="flex flex-1">
      <main className="flex-1 transition-all duration-300">
        {children}
      </main>
    </div>
  )
}
