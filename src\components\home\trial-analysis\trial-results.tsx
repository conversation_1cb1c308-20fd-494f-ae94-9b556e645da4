'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Download, Eye, EyeOff, UserPlus } from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { useTranslation } from '@/hooks/use-translation'

interface TrialAnalysisResult {
  metadata?: any
  markdownContent: string
  docxUrl?: string
}

interface TrialResultsProps {
  analysisResult: TrialAnalysisResult | null
  onSignUpClick: () => void
  trialsRemaining: number
}

export function TrialResults({
  analysisResult,
  onSignUpClick,
  trialsRemaining
}: TrialResultsProps) {
  const { t } = useTranslation('trial')
  const [showMarkdown, setShowMarkdown] = useState(false)

  if (!analysisResult) {
    return null
  }

  const handleDownloadDocx = () => {
    if (analysisResult.docxUrl) {
      const a = document.createElement('a')
      a.href = analysisResult.docxUrl
      a.download = 'trial-analysis-report.docx'
      a.click()
    }
  }

  const handleDownloadMarkdown = () => {
    const blob = new Blob([analysisResult.markdownContent], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'trial-analysis-report.md'
    a.click()
    URL.revokeObjectURL(url)
  }

  return (
    <div className="w-full max-w-none">
      {/* Success Banner with Download Actions */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6 mb-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div>
              <h3 className="text-xl font-semibold text-green-900">
                {t('results.title')}
              </h3>
              <p className="text-green-700">
                {t('results.description')}
              </p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            {/* Trials Remaining */}
            <div className="text-sm text-green-600 font-medium">
              {trialsRemaining > 0 ? (
                <>
                  <span className="text-lg font-bold">{trialsRemaining}</span> {t('results.trialsRemaining')}
                </>
              ) : (
                <span className="text-orange-600">{t('results.noTrialsRemaining')}</span>
              )}
            </div>

            {/* Download Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                onClick={handleDownloadMarkdown}
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <Download className="w-4 h-4 mr-2" />
                Download Markdown (.md)
              </Button>

              {analysisResult.docxUrl && (
                <Button
                  onClick={handleDownloadDocx}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download Word (.docx)
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Results Viewer - Full Width */}
      <div className="w-full bg-white border border-gray-200 rounded-xl overflow-hidden">
        <div className="border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <h4 className="text-lg font-semibold text-gray-900">Analysis Results</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowMarkdown(!showMarkdown)}
              className="flex items-center space-x-2"
            >
              {showMarkdown ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              <span>{showMarkdown ? 'Hide' : 'Show'} Markdown</span>
            </Button>
          </div>
        </div>

        <div className="p-6 w-full">
          {showMarkdown ? (
            <div className="bg-gray-50 rounded-lg p-4 overflow-auto w-full">
              <pre className="text-sm text-gray-700 whitespace-pre-wrap font-mono w-full">
                {analysisResult.markdownContent}
              </pre>
            </div>
          ) : (
            <div className="prose prose-lg max-w-none w-full">
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                  h1: ({children}) => <h1 className="text-3xl font-bold text-gray-900 mb-6 mt-8 first:mt-0">{children}</h1>,
                  h2: ({children}) => <h2 className="text-2xl font-semibold text-gray-800 mb-4 mt-8">{children}</h2>,
                  h3: ({children}) => <h3 className="text-xl font-medium text-gray-700 mb-3 mt-6">{children}</h3>,
                  p: ({children}) => <p className="text-gray-600 mb-4 leading-relaxed break-words">{children}</p>,
                  ul: ({children}) => <ul className="list-disc list-inside mb-6 text-gray-600 space-y-1">{children}</ul>,
                  ol: ({children}) => <ol className="list-decimal list-inside mb-6 text-gray-600 space-y-1">{children}</ol>,
                  li: ({children}) => <li className="mb-1">{children}</li>,
                  blockquote: ({children}) => <blockquote className="border-l-4 border-purple-500 pl-4 py-2 my-4 bg-purple-50 italic">{children}</blockquote>,
                  code: ({children, className}) => {
                    const isInline = !className
                    return isInline
                      ? <code className="bg-gray-100 text-purple-600 px-1 py-0.5 rounded text-sm font-mono">{children}</code>
                      : <code className="block bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm font-mono">{children}</code>
                  },
                  pre: ({children}) => <pre className="bg-gray-900 rounded-lg overflow-x-auto my-4">{children}</pre>,
                  table: ({children}) => <div className="overflow-x-auto my-6"><table className="min-w-full divide-y divide-gray-200 border border-gray-200">{children}</table></div>,
                  thead: ({children}) => <thead className="bg-gray-50">{children}</thead>,
                  tbody: ({children}) => <tbody className="bg-white divide-y divide-gray-200">{children}</tbody>,
                  tr: ({children}) => <tr className="hover:bg-gray-50">{children}</tr>,
                  th: ({children}) => <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200 last:border-r-0">{children}</th>,
                  td: ({children}) => <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200 last:border-r-0 break-words">{children}</td>,
                  hr: () => <hr className="my-8 border-gray-300" />
                }}
              >
                {analysisResult.markdownContent}
              </ReactMarkdown>
            </div>
          )}
        </div>
      </div>

      {/* Call to Action */}
      {trialsRemaining === 0 ? (
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-xl p-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <UserPlus className="w-8 h-8 text-purple-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {t('results.trialsExhausted.title')}
            </h3>
            <p className="text-gray-600 mb-4">
              {t('results.trialsExhausted.description')}
            </p>
            <Button
              onClick={onSignUpClick}
              size="lg"
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
            >
              <UserPlus className="w-5 h-5 mr-2" />
              {t('results.trialsExhausted.button')}
            </Button>
          </div>
        </div>
      ) : (
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
          <div className="text-center">
            <h4 className="text-lg font-semibold text-blue-900 mb-2">
              {t('results.moreTrials.title')}
            </h4>
            <p className="text-blue-800 mb-4">
              {t('results.moreTrials.description', { remaining: trialsRemaining })}
            </p>
            <Button
              onClick={onSignUpClick}
              variant="outline"
              className="border-blue-300 text-blue-700 hover:bg-blue-100"
            >
              <UserPlus className="w-4 h-4 mr-2" />
              {t('results.moreTrials.button')}
            </Button>
          </div>
        </div>
      )}


    </div>
  )
}
