import { SupportedLocale, isSupportedLocale } from '@/hooks/use-translation'

// Storage keys
const LANGUAGE_PREFERENCE_KEY = 'docuchamp-language-preference'
const LANGUAGE_DETECTION_KEY = 'docuchamp-language-detected'

/**
 * Detect user's preferred language based on various factors
 */
export function detectUserLanguage(): SupportedLocale {
  // 1. Check saved user preference (highest priority)
  if (typeof window !== 'undefined') {
    const savedPreference = localStorage.getItem(LANGUAGE_PREFERENCE_KEY)
    if (savedPreference && isSupportedLocale(savedPreference)) {
      return savedPreference
    }
  }

  // 2. Check browser language
  const browserLanguage = detectBrowserLanguage()
  if (browserLanguage) {
    return browserLanguage
  }

  // 3. Fallback to English
  return 'en'
}

/**
 * Detect language from browser settings
 */
export function detectBrowserLanguage(): SupportedLocale | null {
  if (typeof window === 'undefined') {
    return null
  }

  // Get browser languages in order of preference
  const languages = [
    navigator.language,
    ...(navigator.languages || [])
  ]

  for (const lang of languages) {
    const normalizedLang = normalizeBrowserLanguage(lang)
    if (normalizedLang && isSupportedLocale(normalizedLang)) {
      return normalizedLang
    }
  }

  return null
}

/**
 * Normalize browser language code to our supported locales
 */
function normalizeBrowserLanguage(browserLang: string): string | null {
  const lang = browserLang.toLowerCase()

  // Chinese variants
  if (lang.startsWith('zh')) {
    // Traditional Chinese variants
    if (lang.includes('tw') || lang.includes('hk') || lang.includes('mo') || lang.includes('hant')) {
      return 'zh'
    }
    // Simplified Chinese variants
    if (lang.includes('cn') || lang.includes('sg') || lang.includes('hans')) {
      return 'zh' // For now, we use the same 'zh' for both, but this can be expanded
    }
    // Default Chinese
    return 'zh'
  }

  // English variants
  if (lang.startsWith('en')) {
    return 'en'
  }

  return null
}

/**
 * Save user's language preference
 */
export function saveLanguagePreference(locale: SupportedLocale): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem(LANGUAGE_PREFERENCE_KEY, locale)
  }
}

/**
 * Get saved language preference
 */
export function getSavedLanguagePreference(): SupportedLocale | null {
  if (typeof window === 'undefined') {
    return null
  }

  const saved = localStorage.getItem(LANGUAGE_PREFERENCE_KEY)
  return saved && isSupportedLocale(saved) ? saved : null
}

/**
 * Clear saved language preference
 */
export function clearLanguagePreference(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(LANGUAGE_PREFERENCE_KEY)
  }
}

/**
 * Detect if this is the user's first visit (for language detection)
 */
export function isFirstVisit(): boolean {
  if (typeof window === 'undefined') {
    return true
  }

  const hasDetected = localStorage.getItem(LANGUAGE_DETECTION_KEY)
  return !hasDetected
}

/**
 * Mark that language detection has been performed
 */
export function markLanguageDetected(): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem(LANGUAGE_DETECTION_KEY, 'true')
  }
}

/**
 * Get language display name
 */
export function getLanguageDisplayName(locale: SupportedLocale, inLocale?: SupportedLocale): string {
  const names: Record<SupportedLocale, Record<SupportedLocale, string>> = {
    en: {
      en: 'English',
      zh: 'English'
    },
    zh: {
      en: '繁體中文',
      zh: '繁體中文'
    }
  }

  const localeNames = names[locale]
  if (!localeNames) {
    return locale
  }

  return localeNames[inLocale || locale] || locale
}

/**
 * Get language flag emoji
 */
export function getLanguageFlag(locale: SupportedLocale): string {
  const flags: Record<SupportedLocale, string> = {
    en: '🇺🇸',
    zh: '🇭🇰'
  }

  return flags[locale] || '🌐'
}

/**
 * Detect user's region/country (for future geolocation-based detection)
 */
export function detectUserRegion(): string | null {
  if (typeof window === 'undefined') {
    return null
  }

  // Try to get timezone-based region detection
  try {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    
    // Hong Kong, Macau, Taiwan - prefer Traditional Chinese
    if (timezone.includes('Hong_Kong') || timezone.includes('Macau') || timezone.includes('Taipei')) {
      return 'zh-region'
    }
    
    // China, Singapore - could prefer Simplified Chinese (but we use same 'zh' for now)
    if (timezone.includes('Shanghai') || timezone.includes('Singapore')) {
      return 'zh-region'
    }
    
    return 'other'
  } catch (error) {
    console.warn('Failed to detect user region:', error)
    return null
  }
}
