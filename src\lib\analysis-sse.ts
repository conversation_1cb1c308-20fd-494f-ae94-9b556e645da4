import { showAnalysisCompleteNotification, showAnalysisFailedNotification } from '@/lib/notifications'

export interface AnalysisSSECallbacks {
  onProgress?: (progress: number) => void
  onComplete?: (result: any) => void
  onError?: (error: string) => void
  onTimeout?: () => void
  setIsProcessing?: (processing: boolean) => void
  setCurrentOperationId?: (id: string | null) => void
  setAnalysisProgress?: (progress: number) => void
  setAnalysisResult?: (result: any) => void
  setErrorMessage?: (message: string | null) => void
  clearProgressTimer?: () => void
  fileName?: string
  scrollToResults?: () => void
}

export interface AnalysisSSEManager {
  eventSource: EventSource | null
  connect: (operationId: string) => void
  disconnect: () => void
}

export function createAnalysisSSEManager(callbacks: AnalysisSSECallbacks): AnalysisSSEManager {
  let eventSource: EventSource | null = null

  const connect = (operationId: string) => {
    console.log('🚀 Establishing SSE connection for operation:', operationId)
    
    // Close any existing connection first
    if (eventSource) {
      console.log('🧹 Cleaning up existing SSE connection before creating new one')
      eventSource.close()
      eventSource = null
    }

    console.log('🔗 Creating new EventSource for:', `/api/analyze/${operationId}/stream`)
    const es = new EventSource(`/api/analyze/${operationId}/stream`)
    eventSource = es
    console.log('🔗 EventSource created')

    es.onopen = () => {
      console.log('🔌 SSE connection opened for operation:', operationId)
      console.log('🔌 SSE readyState:', es.readyState)
      console.log('🔌 EventSource URL:', es.url)
      console.log('🔌 SSE connection established successfully at:', new Date().toISOString())
    }

    es.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        console.log('📨 SSE message received:', data)

        if (data.type === 'analysis_complete') {
          console.log('🎉 Analysis completed via SSE:', {
            success: data.success,
            hasMarkdown: !!data.markdownContent,
            hasDocxUrl: !!data.output_file_blob_sas_url,
            markdownLength: data.markdownContent?.length || 0
          })

          // Close SSE connection first
          es.close()
          eventSource = null

          if (data.success === true) {
            console.log('✅ Analysis completed successfully via webhook')

            // Stop progress timer immediately
            if (callbacks.clearProgressTimer) {
              callbacks.clearProgressTimer()
            }

            // Set progress to 100% immediately without animation
            if (callbacks.setAnalysisProgress) {
              callbacks.setAnalysisProgress(100)
            }

            // Wait a brief moment at 100% before showing results
            setTimeout(() => {
                  if (callbacks.setIsProcessing) {
                    callbacks.setIsProcessing(false)
                  }
                  if (callbacks.setCurrentOperationId) {
                    callbacks.setCurrentOperationId(null)
                  }

                  const resultData = {
                    metadata: data.metadata,
                    markdownContent: data.markdownContent,
                    docxUrl: data.output_file_blob_sas_url
                  }

                  console.log('Setting analysis result:', {
                    hasMetadata: !!resultData.metadata,
                    hasMarkdown: !!resultData.markdownContent,
                    hasDocxUrl: !!resultData.docxUrl,
                    markdownLength: resultData.markdownContent?.length || 0
                  })

                  if (callbacks.setAnalysisResult) {
                    callbacks.setAnalysisResult(resultData)
                  }

                  if (callbacks.onComplete) {
                    callbacks.onComplete(resultData)
                  }

                  // Scroll to results after a brief delay
                  setTimeout(() => {
                    if (callbacks.scrollToResults) {
                      callbacks.scrollToResults()
                    }
                  }, 300)

                  // Show browser notification
                  showAnalysisCompleteNotification(
                    callbacks.fileName || 'Document',
                    () => {
                      if (callbacks.scrollToResults) {
                        callbacks.scrollToResults()
                      }
                    }
                  )
                }, 300) // Brief pause at 100%
          } else if (data.success === false) {
            console.log('❌ Analysis failed via webhook:', data.error || data.metadata?.error || 'Unknown error')

            // Stop progress timer immediately on failure
            if (callbacks.clearProgressTimer) {
              callbacks.clearProgressTimer()
            }

            // Close SSE connection
            es.close()
            eventSource = null
            
            if (callbacks.setIsProcessing) {
              callbacks.setIsProcessing(false)
            }
            if (callbacks.setAnalysisProgress) {
              callbacks.setAnalysisProgress(0)
            }
            if (callbacks.setCurrentOperationId) {
              callbacks.setCurrentOperationId(null)
            }

            // Use error from webhook payload directly
            const errorMsg = data.error || data.metadata?.error || 'Analysis failed. Please try again.'
            if (callbacks.setErrorMessage) {
              callbacks.setErrorMessage(`Analysis failed: ${errorMsg}`)
            }
            if (callbacks.onError) {
              callbacks.onError(errorMsg)
            }

            // Show failure notification
            showAnalysisFailedNotification(
              callbacks.fileName || 'Document',
              errorMsg
            )
          }
        } else if (data.type === 'connected') {
          console.log('Connected to SSE stream')
        } else if (data.type === 'ping') {
          // Keep-alive ping, do nothing
        } else if (data.type === 'analysis_timeout') {
          console.log('⏰ Analysis timeout received via SSE')
          
          // Close SSE connection
          es.close()
          eventSource = null
          
          if (callbacks.clearProgressTimer) {
            callbacks.clearProgressTimer()
          }
          
          if (callbacks.setIsProcessing) {
            callbacks.setIsProcessing(false)
          }
          if (callbacks.setAnalysisProgress) {
            callbacks.setAnalysisProgress(0)
          }
          if (callbacks.setCurrentOperationId) {
            callbacks.setCurrentOperationId(null)
          }
          
          const timeoutMsg = 'Analysis is taking longer than expected. Please try again or contact support.'
          if (callbacks.setErrorMessage) {
            callbacks.setErrorMessage(timeoutMsg)
          }
          if (callbacks.onTimeout) {
            callbacks.onTimeout()
          }
        }
      } catch (error) {
        console.error('❌ Error parsing SSE message:', error)
        console.error('Raw SSE data:', event.data)
      }
    }

    es.onerror = (error) => {
      console.error('❌ SSE connection error for operation:', operationId)
      console.error('❌ Error details:', error)
      console.error('❌ SSE readyState:', es.readyState)

      // Close connection and clean up immediately on any error
      es.close()
      eventSource = null

      // Stop progress timer immediately
      if (callbacks.clearProgressTimer) {
        callbacks.clearProgressTimer()
      }

      // Stop processing state and show error
      if (callbacks.setIsProcessing) {
        callbacks.setIsProcessing(false)
      }
      if (callbacks.setAnalysisProgress) {
        callbacks.setAnalysisProgress(0)
      }
      if (callbacks.setCurrentOperationId) {
        callbacks.setCurrentOperationId(null)
      }

      // Show error message without auto-retry
      const errorMsg = 'Connection error occurred. Please try again or check your analysis history if the analysis was completed.'
      if (callbacks.setErrorMessage) {
        callbacks.setErrorMessage(errorMsg)
      }
      if (callbacks.onError) {
        callbacks.onError(errorMsg)
      }

      console.log('🔌 SSE connection closed due to error, analysis stopped')
    }
  }

  const disconnect = () => {
    if (eventSource) {
      console.log('🔌 Manually closing SSE connection')
      eventSource.close()
      eventSource = null
    }
  }

  return {
    eventSource,
    connect,
    disconnect
  }
}
