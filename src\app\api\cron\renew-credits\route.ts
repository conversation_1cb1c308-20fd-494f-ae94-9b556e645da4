import { NextRequest, NextResponse } from 'next/server'
import { processAllCreditRenewals } from '@/lib/credit-renewal'

export async function GET(request: NextRequest) {
  try {
    // Verify the request is from a cron job (optional security)
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET
    
    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('Starting credit renewal process...')
    
    const results = await processAllCreditRenewals()
    
    console.log(`Credit renewal completed. Processed ${results.length} renewals.`)
    
    return NextResponse.json({
      success: true,
      message: `Processed ${results.length} credit renewals`,
      results: results.map(r => ({
        userId: r.userId,
        previousCredits: r.previousCredits,
        newCredits: r.newCredits,
        nextRenewalDate: r.nextRenewalDate,
      })),
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error('Error in credit renewal cron job:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  // Allow POST requests as well for manual triggering
  return GET(request)
}
