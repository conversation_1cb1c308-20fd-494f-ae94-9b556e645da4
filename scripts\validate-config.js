#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function validateConfiguration() {
  console.log('🔍 Validating Configuration...\n');
  
  const errors = [];
  const warnings = [];
  
  // Check if configuration files exist
  const configFiles = [
    'config/app.config.json'
  ];
  
  configFiles.forEach(file => {
    if (!fs.existsSync(path.join(__dirname, '..', file))) {
      errors.push(`Missing configuration file: ${file}`);
    }
  });
  
  if (errors.length > 0) {
    console.error('❌ Configuration files missing:');
    errors.forEach(error => console.error(`  - ${error}`));
    return false;
  }
  
  // Load and validate app config
  let appConfig;
  try {
    appConfig = JSON.parse(fs.readFileSync(path.join(__dirname, '../config/app.config.json'), 'utf8'));

    // Validate app settings
    if (!appConfig.app?.name) {
      errors.push('App name is required in app.config.json');
    }

    if (!appConfig.app?.description) {
      warnings.push('App description is recommended in app.config.json');
    }

    // Validate subscription plans
    if (appConfig.billing?.enableFixedPlans && (!appConfig.subscriptionPlans || appConfig.subscriptionPlans.length === 0)) {
      errors.push('At least one subscription plan is required when fixed plans are enabled');
    }

    appConfig.subscriptionPlans?.forEach((plan, index) => {
      if (!plan.stripePriceId || plan.stripePriceId.startsWith('price_')) {
        warnings.push(`Subscription plan ${index + 1} may need a real Stripe price ID`);
      }
      if (!plan.price || plan.price <= 0) {
        errors.push(`Subscription plan ${index + 1} must have a positive price`);
      }
    });

    // Validate credit packages
    if (appConfig.billing?.enableMeteredBilling && (!appConfig.billing.creditPackages || appConfig.billing.creditPackages.length === 0)) {
      errors.push('At least one credit package is required when metered billing is enabled');
    }

    appConfig.billing?.creditPackages?.forEach((pkg, index) => {
      if (!pkg.credits || pkg.credits <= 0) {
        errors.push(`Credit package ${index + 1} must have positive credits`);
      }
      if (!pkg.price || pkg.price <= 0) {
        errors.push(`Credit package ${index + 1} must have a positive price`);
      }
    });

    // Validate document analysis feature
    if (appConfig.features?.documentAnalysis?.enabled && !appConfig.features.documentAnalysis.creditCost) {
      warnings.push('Document analysis is enabled but has no credit cost defined');
    }

  } catch (error) {
    errors.push(`Invalid JSON in app.config.json: ${error.message}`);
  }
  
  // Check environment variables
  const requiredEnvVars = [
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'STRIPE_PUBLISHABLE_KEY',
    'STRIPE_SECRET_KEY',
    'STRIPE_WEBHOOK_SECRET',
    'JWT_SECRET',
    'DOCUMENT_ANALYSIS_API_ENDPOINT',
    'DOCUMENT_ANALYSIS_AUTH_SECRET',
    'NEXT_PUBLIC_ENABLE_DEBUG_TOOLS'
  ];

  const envFile = path.join(__dirname, '../.env.local');
  if (fs.existsSync(envFile)) {
    const envContent = fs.readFileSync(envFile, 'utf8');
    requiredEnvVars.forEach(envVar => {
      if (!envContent.includes(`${envVar}=`)) {
        warnings.push(`Environment variable ${envVar} is not set in .env.local file`);
      }
    });
  } else {
    warnings.push('.env.local file not found. Create it with your environment variables');
  }
  
  // Report results
  console.log('📊 Validation Results:\n');
  
  if (errors.length === 0) {
    console.log('✅ Configuration is valid!');
  } else {
    console.log('❌ Configuration errors found:');
    errors.forEach(error => console.log(`  - ${error}`));
  }
  
  if (warnings.length > 0) {
    console.log('\n⚠️  Warnings:');
    warnings.forEach(warning => console.log(`  - ${warning}`));
  }
  
  console.log('\n📝 Next steps:');
  if (errors.length > 0) {
    console.log('1. Fix the configuration errors above');
    console.log('2. Run this validation again');
  } else {
    console.log('1. Set up your database: npm run db:migrate');
    console.log('2. Configure Stripe products and update price IDs');
    console.log('3. Start development: npm run dev');
  }
  
  return errors.length === 0;
}

if (require.main === module) {
  const isValid = validateConfiguration();
  process.exit(isValid ? 0 : 1);
}

module.exports = { validateConfiguration };
