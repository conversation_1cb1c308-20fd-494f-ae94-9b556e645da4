/**
 * Trial usage tracking for anonymous users
 * Uses a combination of localStorage, cookies, and server-side tracking to prevent bypass
 */

import { setCookie, getCookie } from './utils'
import { v4 as uuidv4 } from 'uuid'

// Constants
const TRIAL_USER_ID_KEY = 'docuchamp_trial_user_id'
const TRIAL_USAGE_COUNT_KEY = 'docuchamp_trial_usage_count'
const TRIAL_LAST_USED_KEY = 'docuchamp_trial_last_used'
const TRIAL_MAX_ATTEMPTS = 3

// Enhanced browser fingerprinting for device-specific tracking
// Designed to work even in incognito/private browsing mode
function getBrowserFingerprint(): string {
  if (typeof window === 'undefined') return 'server'

  try {
    // Hardware-based fingerprinting (works in incognito)
    const hardwareFingerprint = [
      screen.width,
      screen.height,
      screen.colorDepth,
      screen.pixelDepth,
      screen.availWidth,
      screen.availHeight,
      navigator.hardwareConcurrency || 0,
      (navigator as any).deviceMemory || 0,
      navigator.maxTouchPoints || 0,
    ].join('|')

    // System-level fingerprinting (works in incognito)
    const systemFingerprint = [
      navigator.platform,
      navigator.userAgent,
      navigator.language,
      navigator.languages?.join(',') || '',
      new Date().getTimezoneOffset(),
      Intl.DateTimeFormat().resolvedOptions().timeZone,
      navigator.cookieEnabled,
      navigator.doNotTrack || 'null',
    ].join('|')

    // Browser feature detection (works in incognito)
    const featureFingerprint = [
      typeof (window as any).chrome !== 'undefined' ? 'chrome' : 'no-chrome',
      typeof (window as any).safari !== 'undefined' ? 'safari' : 'no-safari',
      typeof (window as any).InstallTrigger !== 'undefined' ? 'firefox' : 'no-firefox',
      typeof (window as any).opr !== 'undefined' ? 'opera' : 'no-opera',
      typeof (window as any).MSInputMethodContext !== 'undefined' ? 'edge' : 'no-edge',
      'ontouchstart' in window ? 'touch' : 'no-touch',
      navigator.webdriver ? 'webdriver' : 'no-webdriver',
    ].join('|')

    // Privacy-friendly canvas fingerprinting (minimal data collection)
    let canvasFingerprint = 'canvas-blocked'
    try {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (ctx) {
        // Use minimal canvas fingerprinting - just basic rendering capabilities
        ctx.font = '12px Arial'
        ctx.fillText('test', 0, 10)
        // Only use a hash of basic canvas capabilities, not the full image data
        const imageData = ctx.getImageData(0, 0, 10, 10)
        let hash = 0
        for (let i = 0; i < Math.min(imageData.data.length, 40); i += 4) {
          hash = ((hash << 5) - hash) + imageData.data[i]
          hash = hash & hash
        }
        canvasFingerprint = Math.abs(hash).toString(36).slice(0, 8)
      }
    } catch (_e) {
      canvasFingerprint = 'canvas-error'
    }

    // WebGL fingerprinting with fallback (may be blocked in incognito)
    let webglFingerprint = 'webgl-blocked'
    try {
      const canvas = document.createElement('canvas')
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
      if (gl && 'getExtension' in gl) {
        const debugInfo = (gl as WebGLRenderingContext).getExtension('WEBGL_debug_renderer_info')
        if (debugInfo) {
          const vendor = (gl as WebGLRenderingContext).getParameter(debugInfo.UNMASKED_VENDOR_WEBGL)
          const renderer = (gl as WebGLRenderingContext).getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
          webglFingerprint = `${vendor}|${renderer}`.slice(0, 50)
        } else {
          webglFingerprint = 'webgl-no-debug'
        }
      }
    } catch (_e) {
      webglFingerprint = 'webgl-error'
    }

    // Audio fingerprinting with fallback (may be blocked in incognito)
    let audioFingerprint = 'audio-blocked'
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      audioFingerprint = `${audioContext.sampleRate}|${audioContext.state}|${audioContext.baseLatency || 0}`
      audioContext.close()
    } catch (_e) {
      audioFingerprint = 'audio-error'
    }

    // Simplified font detection (privacy-friendly)
    let fontFingerprint = 'fonts-unknown'
    try {
      // Only test for common system fonts, not detailed font enumeration
      const testFonts = ['Arial', 'Times', 'Courier']
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (ctx) {
        const testString = 'test'
        const detectedFonts: string[] = []

        for (const font of testFonts) {
          ctx.font = '12px ' + font
          const width = ctx.measureText(testString).width
          if (width > 0) {
            detectedFonts.push(font)
          }
        }
        fontFingerprint = detectedFonts.length.toString() // Just count, not specific fonts
      }
    } catch (_e) {
      fontFingerprint = 'fonts-error'
    }

    // Combine all fingerprints with weights (prioritize hardware/system over blockable features)
    const combinedFingerprint = [
      `hw:${hardwareFingerprint}`,
      `sys:${systemFingerprint}`,
      `feat:${featureFingerprint}`,
      `font:${fontFingerprint}`,
      `canvas:${canvasFingerprint}`,
      `webgl:${webglFingerprint}`,
      `audio:${audioFingerprint}`,
    ].join('||')

    // Create a robust hash
    let hash = 0
    for (let i = 0; i < combinedFingerprint.length; i++) {
      const char = combinedFingerprint.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }

    const fingerprintHash = Math.abs(hash).toString(36)

    // Add additional entropy from stable browser characteristics
    const entropy = [
      window.devicePixelRatio || 1,
      window.innerWidth,
      window.innerHeight,
      window.outerWidth,
      window.outerHeight,
      window.screen?.orientation?.type || 'unknown',
    ].join('|')

    return `${fingerprintHash}_${btoa(entropy).slice(0, 10)}`

  } catch (error) {
    console.error('Fingerprinting error:', error)
    // Robust fallback using only stable, non-blockable features
    const fallbackData = [
      navigator.userAgent || 'unknown',
      navigator.platform || 'unknown',
      screen.width || 0,
      screen.height || 0,
      screen.colorDepth || 0,
      new Date().getTimezoneOffset(),
      navigator.language || 'unknown',
      navigator.hardwareConcurrency || 0,
      window.devicePixelRatio || 1,
    ].join('|')

    let fallbackHash = 0
    for (let i = 0; i < fallbackData.length; i++) {
      const char = fallbackData.charCodeAt(i)
      fallbackHash = ((fallbackHash << 5) - fallbackHash) + char
      fallbackHash = fallbackHash & fallbackHash
    }

    return `fallback_${Math.abs(fallbackHash).toString(36)}`
  }
}

interface TrialUsageInfo {
  userId: string
  usageCount: number
  lastUsed: string
  attemptsRemaining: number
  canUseTrialNow: boolean
}



/**
 * Get or create anonymous user ID
 * Uses multiple storage mechanisms including fingerprint-based ID for incognito mode
 */
export function getAnonymousUserId(): string {
  // Try to get from localStorage first (works in normal browsing)
  let userId = typeof window !== 'undefined' ? localStorage.getItem(TRIAL_USER_ID_KEY) : null

  // If not in localStorage, try cookies (works in normal browsing)
  if (!userId) {
    userId = getCookie(TRIAL_USER_ID_KEY)
  }

  // If still no userId (likely incognito mode), generate a deterministic ID based on fingerprint
  if (!userId) {
    const fingerprint = getBrowserFingerprint()
    // Create a deterministic userId from fingerprint for incognito consistency
    userId = `anon_${fingerprint.slice(0, 16)}`

    // Try to store in localStorage and cookies (may fail in incognito)
    try {
      if (typeof window !== 'undefined') {
        localStorage.setItem(TRIAL_USER_ID_KEY, userId)
      }
      setCookie(TRIAL_USER_ID_KEY, userId, 365) // 1 year expiry
    } catch (_e) {
      // Storage may be blocked in incognito mode, that's okay
      console.log('Storage blocked, using fingerprint-based ID')
    }
  }

  // Ensure consistency between localStorage and cookies when possible
  try {
    if (typeof window !== 'undefined') {
      localStorage.setItem(TRIAL_USER_ID_KEY, userId)
    }
    setCookie(TRIAL_USER_ID_KEY, userId, 365)
  } catch (_e) {
    // Storage may be blocked, continue with the userId we have
  }

  return userId
}

/**
 * Get current trial usage count with enhanced tracking
 */
export function getTrialUsageCount(): number {
  // Try localStorage first
  let usageCount = 0

  if (typeof window !== 'undefined') {
    const storedCount = localStorage.getItem(TRIAL_USAGE_COUNT_KEY)
    if (storedCount) {
      usageCount = parseInt(storedCount, 10)
    }
  }

  // Check cookies as backup
  const cookieCount = getCookie(TRIAL_USAGE_COUNT_KEY)
  if (cookieCount && (!usageCount || parseInt(cookieCount, 10) > usageCount)) {
    usageCount = parseInt(cookieCount, 10)
  }

  // For incognito mode, also check fingerprint-based storage
  const fingerprint = getBrowserFingerprint()
  const fingerprintKey = `${TRIAL_USAGE_COUNT_KEY}_${fingerprint.slice(0, 10)}`

  try {
    if (typeof window !== 'undefined') {
      const fingerprintCount = localStorage.getItem(fingerprintKey)
      if (fingerprintCount && parseInt(fingerprintCount, 10) > usageCount) {
        usageCount = parseInt(fingerprintCount, 10)
      }
    }

    const fingerprintCookieCount = getCookie(fingerprintKey)
    if (fingerprintCookieCount && parseInt(fingerprintCookieCount, 10) > usageCount) {
      usageCount = parseInt(fingerprintCookieCount, 10)
    }
  } catch (_e) {
    // Fingerprint storage may fail, continue with what we have
  }

  return isNaN(usageCount) ? 0 : usageCount
}

/**
 * Get last trial usage timestamp
 */
export function getLastTrialUsage(): string | null {
  // Try localStorage first
  let lastUsed = typeof window !== 'undefined' ? localStorage.getItem(TRIAL_LAST_USED_KEY) : null
  
  // Check cookies as backup
  if (!lastUsed) {
    lastUsed = getCookie(TRIAL_LAST_USED_KEY)
  }
  
  return lastUsed
}

/**
 * Record a trial usage (both client-side and server-side with enhanced tracking)
 */
export async function recordTrialUsage(): Promise<void> {
  const currentCount = getTrialUsageCount()
  const newCount = currentCount + 1
  const now = new Date().toISOString()
  const fingerprint = getBrowserFingerprint()
  const userId = getAnonymousUserId()

  // Update standard localStorage and cookies
  try {
    if (typeof window !== 'undefined') {
      localStorage.setItem(TRIAL_USAGE_COUNT_KEY, newCount.toString())
      localStorage.setItem(TRIAL_LAST_USED_KEY, now)
    }
    setCookie(TRIAL_USAGE_COUNT_KEY, newCount.toString(), 365)
    setCookie(TRIAL_LAST_USED_KEY, now, 365)
  } catch (_e) {
    // Fallback to fingerprint storage when standard storage is blocked
  }

  // Also update fingerprint-based storage for incognito mode
  const fingerprintKey = `${TRIAL_USAGE_COUNT_KEY}_${fingerprint.slice(0, 10)}`
  const fingerprintLastUsedKey = `${TRIAL_LAST_USED_KEY}_${fingerprint.slice(0, 10)}`

  try {
    if (typeof window !== 'undefined') {
      localStorage.setItem(fingerprintKey, newCount.toString())
      localStorage.setItem(fingerprintLastUsedKey, now)
    }
    setCookie(fingerprintKey, newCount.toString(), 365)
    setCookie(fingerprintLastUsedKey, now, 365)
  } catch (_e) {
    // Even fingerprint storage may be blocked in strict incognito mode
    console.log('Fingerprint storage also blocked')
  }

  // Record usage on server-side as well (most important for incognito mode)
  try {
    const response = await fetch('/api/trial/record', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        fingerprint,
        timestamp: now
      })
    })

    if (!response.ok) {
      throw new Error(`Server recording failed: ${response.status}`)
    }
  } catch (error) {
    console.error('Failed to record trial usage on server:', error)
    // Continue even if server recording fails, but this is critical for incognito mode
  }
}

/**
 * Check if user can use trial now (client-side check)
 */
export function canUseTrialNow(): boolean {
  const usageCount = getTrialUsageCount()
  return usageCount < TRIAL_MAX_ATTEMPTS
}

/**
 * Enhanced client-side trial validation with server-side verification
 * Uses device fingerprinting instead of IP addresses to avoid shared network issues
 */
export async function validateTrialUsageEnhanced(): Promise<{
  canUseTrial: boolean
  remainingAttempts: number
  fingerprint: string
}> {
  try {
    // Get browser fingerprint
    const fingerprint = getBrowserFingerprint()
    const userId = getAnonymousUserId()

    // Check server-side trial usage using fingerprint only
    const response = await fetch('/api/trial/validate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fingerprint: fingerprint,
        userId: userId
      })
    })

    if (!response.ok) {
      // If server validation fails, fall back to client-side check
      const clientCanUse = canUseTrialNow()
      const clientRemaining = getRemainingTrialAttempts()

      return {
        canUseTrial: clientCanUse,
        remainingAttempts: clientRemaining,
        fingerprint: fingerprint
      }
    }

    const data = await response.json()

    // Combine server and client-side checks (use the more restrictive one)
    const clientCanUse = canUseTrialNow()
    const clientRemaining = getRemainingTrialAttempts()

    return {
      canUseTrial: data.canUseTrial && clientCanUse,
      remainingAttempts: Math.min(data.remainingAttempts, clientRemaining),
      fingerprint: fingerprint
    }
  } catch (error) {
    console.error('Enhanced trial validation error:', error)
    // On error, fall back to client-side validation only
    return {
      canUseTrial: canUseTrialNow(),
      remainingAttempts: getRemainingTrialAttempts(),
      fingerprint: getBrowserFingerprint()
    }
  }
}

/**
 * Get remaining trial attempts
 */
export function getRemainingTrialAttempts(): number {
  const usageCount = getTrialUsageCount()
  return Math.max(0, TRIAL_MAX_ATTEMPTS - usageCount)
}

/**
 * Get complete trial usage information
 */
export function getTrialUsageInfo(): TrialUsageInfo {
  const userId = getAnonymousUserId()
  const usageCount = getTrialUsageCount()
  const lastUsed = getLastTrialUsage() || new Date().toISOString()
  const attemptsRemaining = getRemainingTrialAttempts()
  const canUseTrialNow = attemptsRemaining > 0
  
  return {
    userId,
    usageCount,
    lastUsed,
    attemptsRemaining,
    canUseTrialNow
  }
}

/**
 * Clear trial usage data (for testing purposes)
 */
export function clearTrialUsageData(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(TRIAL_USER_ID_KEY)
    localStorage.removeItem(TRIAL_USAGE_COUNT_KEY)
    localStorage.removeItem(TRIAL_LAST_USED_KEY)
  }

  // Clear cookies
  setCookie(TRIAL_USER_ID_KEY, '', -1)
  setCookie(TRIAL_USAGE_COUNT_KEY, '', -1)
  setCookie(TRIAL_LAST_USED_KEY, '', -1)
}

/**
 * Set trial usage to maximum (for testing exhausted trials)
 */
export function setTrialsExhausted(): void {
  const now = new Date().toISOString()

  // Set usage count to maximum
  if (typeof window !== 'undefined') {
    localStorage.setItem(TRIAL_USAGE_COUNT_KEY, TRIAL_MAX_ATTEMPTS.toString())
    localStorage.setItem(TRIAL_LAST_USED_KEY, now)
  }

  setCookie(TRIAL_USAGE_COUNT_KEY, TRIAL_MAX_ATTEMPTS.toString(), 365)
  setCookie(TRIAL_LAST_USED_KEY, now, 365)
}
