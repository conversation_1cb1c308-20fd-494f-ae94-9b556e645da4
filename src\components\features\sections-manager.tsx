'use client'

import React, { useState, useEffect } from 'react'
import { Plus, Edit2, Trash2, Check, X, GripVertical, Save, Upload } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { TemplateModal } from './template-modal'
import documentAnalysisConfig from '../../../config/document-analysis.config.json'

interface SectionsManagerProps {
  selectedUseCase: string | null
  selectedSections: string[]
  onSectionsChange: (sections: string[]) => void
  className?: string
}

export function SectionsManager({ 
  selectedUseCase, 
  selectedSections, 
  onSectionsChange, 
  className = '' 
}: SectionsManagerProps) {
  const [editingSection, setEditingSection] = useState<string | null>(null)
  const [editValue, setEditValue] = useState('')
  const [newSectionName, setNewSectionName] = useState('')
  const [showAddForm, setShowAddForm] = useState(false)
  const [templateModal, setTemplateModal] = useState<{ isOpen: boolean; mode: 'save' | 'load' }>({
    isOpen: false,
    mode: 'save'
  })

  const useCaseData = selectedUseCase 
    ? documentAnalysisConfig.useCases[selectedUseCase as keyof typeof documentAnalysisConfig.useCases]
    : null

  // Initialize with default sections when use case changes
  useEffect(() => {
    if (useCaseData) {
      onSectionsChange([...useCaseData.sections.default])
    }
  }, [selectedUseCase, useCaseData, onSectionsChange])

  if (!selectedUseCase || !useCaseData) {
    return (
      <div className={`bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center ${className}`}>
        <div className="text-gray-500">
          <Edit2 className="w-8 h-8 mx-auto mb-2 opacity-50" />
          <p className="font-medium">Select an analysis type</p>
          <p className="text-sm">Choose an analysis type above to configure sections</p>
        </div>
      </div>
    )
  }

  const handleAddSection = () => {
    if (newSectionName.trim()) {
      onSectionsChange([...selectedSections, newSectionName.trim()])
      setNewSectionName('')
      setShowAddForm(false)
    }
  }

  const handleEditSection = (index: number, newName: string) => {
    const updatedSections = [...selectedSections]
    updatedSections[index] = newName
    onSectionsChange(updatedSections)
    setEditingSection(null)
    setEditValue('')
  }

  const handleRemoveSection = (index: number) => {
    const updatedSections = selectedSections.filter((_, i) => i !== index)
    onSectionsChange(updatedSections)
  }

  const handleAddOptionalSection = (sectionName: string) => {
    if (!selectedSections.includes(sectionName)) {
      onSectionsChange([...selectedSections, sectionName])
    }
  }

  const startEditing = (index: number, currentName: string) => {
    setEditingSection(index.toString())
    setEditValue(currentName)
  }

  const cancelEditing = () => {
    setEditingSection(null)
    setEditValue('')
  }



  const handleSaveTemplate = () => {
    setTemplateModal({ isOpen: true, mode: 'save' })
  }

  const handleLoadTemplate = () => {
    setTemplateModal({ isOpen: true, mode: 'load' })
  }

  const handleCloseTemplateModal = () => {
    setTemplateModal({ isOpen: false, mode: 'save' })
  }

  const handleLoadTemplateData = (sections: string[]) => {
    onSectionsChange(sections)
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Analysis Sections</h3>
        <div className="flex flex-wrap items-center gap-2">
          <Button
            onClick={handleLoadTemplate}
            size="sm"
            variant="outline"
            className="border-blue-300 text-blue-700 hover:bg-blue-50 flex-shrink-0"
            title="Load a previously saved template configuration"
          >
            <Upload className="w-4 h-4 mr-1" />
            <span className="hidden xs:inline">Load</span>
          </Button>
          <Button
            onClick={handleSaveTemplate}
            size="sm"
            variant="outline"
            className="border-green-300 text-green-700 hover:bg-green-50 flex-shrink-0"
            title="Save current sections as a template for future use"
          >
            <Save className="w-4 h-4 mr-1" />
            <span className="hidden xs:inline">Save</span>
          </Button>
          <Button
            onClick={() => setShowAddForm(true)}
            size="sm"
            className="bg-purple-600 hover:bg-purple-700 text-white flex-shrink-0"
          >
            <Plus className="w-4 h-4 mr-1" />
            Add
          </Button>
        </div>
      </div>

      {/* Current Sections */}
      <div className="space-y-2 mb-6">
        {selectedSections.map((section, index) => (
          <div
            key={index}
            className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg border border-gray-200"
          >
            <GripVertical className="w-4 h-4 text-gray-400 cursor-move" />
            
            {editingSection === index.toString() ? (
              <div className="flex-1 flex items-center space-x-2">
                <input
                  type="text"
                  value={editValue}
                  onChange={(e) => setEditValue(e.target.value)}
                  className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                  autoFocus
                />
                <button
                  onClick={() => handleEditSection(index, editValue)}
                  className="p-1 text-green-600 hover:bg-green-100 rounded"
                >
                  <Check className="w-4 h-4" />
                </button>
                <button
                  onClick={cancelEditing}
                  className="p-1 text-gray-500 hover:bg-gray-100 rounded"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ) : (
              <>
                <span className="flex-1 text-sm font-medium text-gray-900">{section}</span>
                <button
                  onClick={() => startEditing(index, section)}
                  className="p-1 text-gray-500 hover:bg-gray-200 rounded"
                >
                  <Edit2 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleRemoveSection(index)}
                  className="p-1 text-red-500 hover:bg-red-100 rounded"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </>
            )}
          </div>
        ))}
      </div>

      {/* Add New Section Form */}
      {showAddForm && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={newSectionName}
              onChange={(e) => setNewSectionName(e.target.value)}
              placeholder="Enter section name..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
              autoFocus
            />
            <Button
              onClick={handleAddSection}
              size="sm"
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <Check className="w-4 h-4" />
            </Button>
            <Button
              onClick={() => {
                setShowAddForm(false)
                setNewSectionName('')
              }}
              size="sm"
              variant="outline"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Optional Sections */}
      {useCaseData.sections.optional && Array.isArray(useCaseData.sections.optional) && useCaseData.sections.optional.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">Optional Sections</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {useCaseData.sections.optional.map((sectionName, index) => {
              const isAdded = selectedSections.includes(sectionName)
              return (
                <button
                  key={index}
                  onClick={() => handleAddOptionalSection(sectionName)}
                  disabled={isAdded}
                  className={`
                    p-3 text-left text-sm rounded-lg border transition-colors
                    ${isAdded
                      ? 'bg-green-50 border-green-200 text-green-700 cursor-not-allowed'
                      : 'bg-white border-gray-200 text-gray-700 hover:bg-purple-50 hover:border-purple-200'
                    }
                  `}
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{sectionName}</span>
                    {isAdded ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : (
                      <Plus className="w-4 h-4 text-gray-400" />
                    )}
                  </div>
                </button>
              )
            })}
          </div>
        </div>
      )}

      {/* Template Modal */}
      <TemplateModal
        isOpen={templateModal.isOpen}
        onClose={handleCloseTemplateModal}
        mode={templateModal.mode}
        useCaseId={selectedUseCase}
        currentSections={selectedSections}
        onLoadTemplate={handleLoadTemplateData}
      />
    </div>
  )
}
