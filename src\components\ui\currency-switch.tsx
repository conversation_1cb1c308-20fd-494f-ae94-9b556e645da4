'use client'

// import { useState } from 'react' // Unused import
import { Button } from '@/components/ui/button'

interface CurrencySwitchProps {
  currentCurrency: 'HKD' | 'USD'
  onCurrencyChange: (currency: 'HKD' | 'USD') => void
  className?: string
}

export function CurrencySwitch({ currentCurrency, onCurrencyChange, className = '' }: CurrencySwitchProps) {
  return (
    <div className={`flex items-center bg-gray-100 rounded-lg p-1 ${className}`}>
      <Button
        variant={currentCurrency === 'HKD' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onCurrencyChange('HKD')}
        className={`px-3 py-1 text-sm font-medium transition-all ${
          currentCurrency === 'HKD'
            ? 'bg-white text-gray-900 shadow-sm'
            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
        }`}
      >
        HKD
      </Button>
      <Button
        variant={currentCurrency === 'USD' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onCurrencyChange('USD')}
        className={`px-3 py-1 text-sm font-medium transition-all ${
          currentCurrency === 'USD'
            ? 'bg-white text-gray-900 shadow-sm'
            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
        }`}
      >
        USD
      </Button>
    </div>
  )
}
