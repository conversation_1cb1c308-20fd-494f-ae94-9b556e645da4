'use client'

import React from 'react'
import { UseCaseSelector } from '@/components/features/use-case-selector'
import { SectionsManager } from '@/components/features/sections-manager'

interface AnalysisSettingsProps {
  selectedUseCase: string | null;
  selectedSections: string[];
  language: string;
  customLanguage: string;
  reportLength: string;
  onUseCaseChange: (useCase: string | null) => void;
  onSectionsChange: (sections: string[]) => void;
  onLanguageChange: (language: string) => void;
  onCustomLanguageChange: (customLanguage: string) => void;
  onReportLengthChange: (length: string) => void;
}

const reportLengthOptions = [
  { value: 'short', label: 'Short (500 words)', description: 'Concise summary with key points' },
  { value: 'medium', label: 'Medium (1000 words)', description: 'Balanced detail with comprehensive insights' },
  { value: 'long', label: 'Long (2000 words)', description: 'Detailed analysis with extensive coverage' }
];

const languageOptions = [
  'English',
  'Chinese (Traditional)',
  'Chinese (Simplified)',
  'Spanish',
  'French',
  'German',
  'Japanese',
  'Korean',
  'custom'
];

export function AnalysisSettings({
  selectedUseCase,
  selectedSections,
  language,
  customLanguage,
  reportLength,
  onUseCaseChange,
  onSectionsChange,
  onLanguageChange,
  onCustomLanguageChange,
  onReportLengthChange
}: AnalysisSettingsProps) {
  return (
    <div className="space-y-8">
      {/* Use Case Selection */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          1. Select Analysis Type
        </h3>
        <UseCaseSelector
          selectedUseCase={selectedUseCase}
          onUseCaseChange={onUseCaseChange}
        />
      </div>

      {/* Sections Management */}
      {selectedUseCase && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            2. Customize Report Sections
          </h3>
          <SectionsManager
            selectedUseCase={selectedUseCase}
            selectedSections={selectedSections}
            onSectionsChange={onSectionsChange}
          />
        </div>
      )}

      {/* Report Settings */}
      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">
          3. Report Settings
        </h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Language Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Report Language
            </label>
            <select
              value={language}
              onChange={(e) => onLanguageChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
              {languageOptions.map((lang) => (
                <option key={lang} value={lang}>
                  {lang === 'custom' ? 'Custom Language...' : lang}
                </option>
              ))}
            </select>
            
            {language === 'custom' && (
              <div className="mt-3">
                <input
                  type="text"
                  placeholder="Enter your preferred language"
                  value={customLanguage}
                  onChange={(e) => onCustomLanguageChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
            )}
          </div>

          {/* Report Length Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Report Length
            </label>
            <div className="space-y-3">
              {reportLengthOptions.map((option) => (
                <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="reportLength"
                    value={option.value}
                    checked={reportLength === option.value}
                    onChange={(e) => onReportLengthChange(e.target.value)}
                    className="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300"
                  />
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900">
                      {option.label}
                    </div>
                    <div className="text-xs text-gray-500">
                      {option.description}
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
