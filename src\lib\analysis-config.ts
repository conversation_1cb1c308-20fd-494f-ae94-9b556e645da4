/**
 * Configuration for document analysis templates and default sections
 * This imports from the main JSON config file used by the frontend
 */

import documentAnalysisConfig from '../../config/document-analysis.config.json'

export interface AnalysisSection {
  id: string
  name: string
  description: string
  required: boolean
}

export interface AnalysisTemplate {
  id: string
  name: string
  description: string
  defaultSections: AnalysisSection[]
}

// Helper function to convert JSON config sections to AnalysisSection format
function convertToAnalysisSections(sectionNames: string[], allRequired = false): AnalysisSection[] {
  return sectionNames.map(name => ({
    id: name.toLowerCase().replace(/\s+/g, '_'),
    name,
    description: `Analysis of ${name.toLowerCase()}`,
    required: allRequired
  }))
}

// Get all analysis templates from the JSON config
export const ANALYSIS_TEMPLATES: AnalysisTemplate[] = Object.entries(documentAnalysisConfig.useCases).map(
  ([id, useCase]) => ({
    id,
    name: useCase.name,
    description: useCase.description,
    defaultSections: convertToAnalysisSections(useCase.sections.default, true)
  })
)

/**
 * Get default sections for a specific analysis type
 */
export function getDefaultSections(analysisType: string): AnalysisSection[] {
  const template = ANALYSIS_TEMPLATES.find(t => t.id === analysisType)
  return template?.defaultSections || ANALYSIS_TEMPLATES.find(t => t.id === 'office')?.defaultSections || []
}

/**
 * Get default section names for a specific analysis type
 */
export function getDefaultSectionNames(analysisType: string): string[] {
  // Get sections directly from JSON config for more accurate data
  const useCase = documentAnalysisConfig.useCases[analysisType as keyof typeof documentAnalysisConfig.useCases]
  if (useCase) {
    return useCase.sections.default
  }

  // Fallback to office document sections
  const officeUseCase = documentAnalysisConfig.useCases.office
  return officeUseCase.sections.default
}

/**
 * Get required sections for a specific analysis type
 */
export function getRequiredSections(analysisType: string): AnalysisSection[] {
  const sections = getDefaultSections(analysisType)
  return sections.filter(section => section.required)
}
