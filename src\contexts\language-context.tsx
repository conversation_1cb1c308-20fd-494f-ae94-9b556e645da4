'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

export type SupportedLocale = 'en' | 'zh'

interface LanguageContextType {
  locale: SupportedLocale
  setLocale: (locale: SupportedLocale) => void
  isLoading: boolean
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

interface LanguageProviderProps {
  children: ReactNode
}

export function LanguageProvider({ children }: LanguageProviderProps) {
  const [locale, setLocaleState] = useState<SupportedLocale>('en')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Initialize locale from localStorage or browser preference
    const initializeLocale = () => {
      try {
        // Check saved preference
        const saved = localStorage.getItem('docuchamp-language-preference')
        if (saved && (saved === 'en' || saved === 'zh')) {
          setLocaleState(saved)
          return
        }

        // Browser language detection
        const browserLang = navigator.language.toLowerCase()
        if (browserLang.startsWith('zh')) {
          setLocaleState('zh')
        } else {
          setLocaleState('en')
        }
      } catch (error) {
        console.warn('Error initializing locale:', error)
        setLocaleState('en')
      } finally {
        setIsLoading(false)
      }
    }

    initializeLocale()
  }, [])

  const setLocale = (newLocale: SupportedLocale) => {
    setLocaleState(newLocale)
    try {
      localStorage.setItem('docuchamp-language-preference', newLocale)
    } catch (error) {
      console.warn('Error saving locale preference:', error)
    }
  }

  return (
    <LanguageContext.Provider value={{ locale, setLocale, isLoading }}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}
