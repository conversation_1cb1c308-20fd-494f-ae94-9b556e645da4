import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { getUserUsageHistory } from '@/lib/credits'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const userId = (session?.user as any)?.id
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')

    const usage = await getUserUsageHistory(userId, limit)

    // Group usage by date and sum credits
    const usageByDate = new Map<string, number>()

    usage.forEach(u => {
      const dateStr = u.createdAt.toISOString().split('T')[0] // Get YYYY-MM-DD format
      const currentCount = usageByDate.get(dateStr) || 0
      usageByDate.set(dateStr, currentCount + (u.creditSpent || 1))
    })

    // Convert to array format expected by the chart
    const formattedUsage = Array.from(usageByDate.entries()).map(([date, count]) => ({
      date: new Date(date).toISOString(),
      count,
    }))

    return NextResponse.json({ usage: formattedUsage })
  } catch (error) {
    console.error('Error fetching usage history:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
