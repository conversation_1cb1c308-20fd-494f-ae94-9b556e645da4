export interface Template {
  id: string
  name: string
  sections: string[]
  createdAt: string
  useCaseId: string
  isPublic?: boolean
  usageCount?: number
}

const MAX_TEMPLATES_PER_USE_CASE = 5

export class TemplateStorage {
  // Database-based methods (for server-side)
  static async getTemplatesForUseCaseDB(userId: string, useCaseId: string): Promise<Template[]> {
    try {
      const response = await fetch(`/api/templates?useCaseId=${useCaseId}`)
      if (!response.ok) throw new Error('Failed to fetch templates')
      return await response.json()
    } catch (error) {
      console.error('Error loading templates from DB:', error)
      return []
    }
  }

  static async saveTemplateDB(useCaseId: string, name: string, sections: string[]): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch('/api/templates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ useCaseId, name, sections })
      })

      const result = await response.json()
      return { success: response.ok, error: result.error }
    } catch (error) {
      console.error('Error saving template to DB:', error)
      return { success: false, error: 'Network error' }
    }
  }

  static async deleteTemplateDB(templateId: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/templates/${templateId}`, {
        method: 'DELETE'
      })
      return response.ok
    } catch (error) {
      console.error('Error deleting template from DB:', error)
      return false
    }
  }

  static async incrementUsageDB(templateId: string): Promise<void> {
    try {
      await fetch(`/api/templates/${templateId}/usage`, {
        method: 'POST'
      })
    } catch (error) {
      console.error('Error incrementing template usage:', error)
    }
  }

  // Legacy localStorage methods (fallback)
  static getAllTemplatesLocal(): Template[] {
    if (typeof window === 'undefined') return []

    try {
      const stored = localStorage.getItem('document-analysis-templates')
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.error('Error loading templates from localStorage:', error)
      return []
    }
  }

  static getMaxTemplatesPerUseCase(): number {
    return MAX_TEMPLATES_PER_USE_CASE
  }
}
