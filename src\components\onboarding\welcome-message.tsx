'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { Gift, X } from 'lucide-react'

interface WelcomeMessageProps {
  isOpen: boolean
  onClose: () => void
  userCredits: number
}

export function WelcomeMessage({ isOpen, onClose, userCredits }: WelcomeMessageProps) {
  if (!isOpen) return null

  return (
    <div className="auth-modal-backdrop fixed inset-0 z-50 flex items-center justify-center">
      {/* Welcome Modal */}
      <div className="auth-modal-content relative bg-white rounded-lg shadow-xl border border-gray-200 p-8 max-w-md mx-4">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600"
        >
          <X className="w-5 h-5" />
        </button>

        {/* Content */}
        <div className="text-center">
          {/* Icon */}
          <div className="mx-auto flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mb-4">
            <Gift className="w-8 h-8 text-purple-600" />
          </div>

          {/* Title */}
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Welcome to DocuChampAI! 🎉
          </h2>

          {/* Message */}
          <p className="text-gray-600 mb-6">
            Congratulations! You have received <span className="font-semibold text-purple-600">{userCredits} free credits</span> to get started with AI-powered document analysis.
          </p>

          {/* Button */}
          <div className="flex justify-center">
            <Button
              onClick={onClose}
              className="bg-purple-600 hover:bg-purple-700 text-white"
            >
              Get Started
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
