import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { stripe } from '@/lib/stripe'
import { env } from '@/lib/config'
import { prisma } from '@/lib/prisma'
import Stripe from 'stripe'
import {
  handleSubscriptionChange,
  handleSubscriptionDeleted,
  handleSubscriptionCancelScheduled,
  handlePaymentSucceeded,
  handlePaymentFailed,
} from '@/lib/stripe-utils'

// Idempotency check function
async function checkAndMarkEventProcessed(stripeEventId: string, eventType: string): Promise<boolean> {
  try {
    // Check if event already exists using raw SQL for now
    const existingEvent = await prisma.$queryRaw`
      SELECT id FROM processed_events WHERE stripeEventId = ${stripeEventId} LIMIT 1
    `

    if (Array.isArray(existingEvent) && existingEvent.length > 0) {
      console.log(`⚠️ Event ${stripeEventId} already processed, skipping`)
      return false // Event already processed
    }

    // Create new event record using raw SQL
    await prisma.$executeRaw`
      INSERT INTO processed_events (id, stripeEventId, eventType, processed, createdAt, metadata)
      VALUES (${generateId()}, ${stripeEventId}, ${eventType}, false, NOW(), ${JSON.stringify({
        receivedAt: new Date().toISOString()
      })})
    `

    return true // Event is new, proceed with processing
  } catch (error: any) {
    console.error('❌ Error checking event idempotency:', error)

    // Check if this is a duplicate key error (event already exists)
    if (error.code === 'P2010' && error.message?.includes('Duplicate entry')) {
      console.log(`⚠️ Event ${stripeEventId} already exists (duplicate key error), skipping`)
      return false // Event already processed, don't process again
    }

    // For other errors, fail safe and don't process
    console.error(`❌ Unexpected error during idempotency check for ${stripeEventId}, skipping to prevent duplicates`)
    return false
  }
}

// Mark event as successfully processed
async function markEventProcessed(stripeEventId: string): Promise<void> {
  try {
    await prisma.$executeRaw`
      UPDATE processed_events
      SET processed = true, processedAt = NOW()
      WHERE stripeEventId = ${stripeEventId}
    `

    // Clean up old events (keep only last 30 days)
    await cleanupOldEvents()
  } catch (error) {
    console.error('❌ Error marking event as processed:', error)
  }
}

// Cleanup old processed events to prevent table growth
async function cleanupOldEvents(): Promise<void> {
  try {
    // Delete events older than 30 days
    const result = await prisma.$executeRaw`
      DELETE FROM processed_events
      WHERE createdAt < DATE_SUB(NOW(), INTERVAL 30 DAY)
    `

    // Log cleanup if any rows were affected
    if (typeof result === 'number' && result > 0) {
      console.log(`🧹 Cleaned up ${result} old webhook events`)
    }
  } catch (error) {
    console.error('❌ Error cleaning up old events:', error)
    // Don't throw - cleanup failure shouldn't break webhook processing
  }
}

// Simple ID generator (similar to cuid)
function generateId(): string {
  return 'evt_' + Math.random().toString(36).substring(2, 11) + Date.now().toString(36)
}

export async function POST(request: NextRequest) {
  // console.log('🔔 Webhook received at:', new Date().toISOString())
  const body = await request.text()
  const headersList = await headers()
  const signature = headersList.get('stripe-signature')
  // console.log('📝 Body length:', body.length, 'Signature present:', !!signature)
  // console.log('🔑 Webhook secret configured:', env.STRIPE_WEBHOOK_SECRET !== 'whsec_your_stripe_webhook_secret')

  // Log the raw body for debugging (first 500 chars)
  // console.log('📦 Raw webhook body preview:', body.substring(0, 500))

  if (!signature) {
    console.error('❌ Missing stripe-signature header')
    return NextResponse.json(
      { error: 'Missing stripe-signature header' },
      { status: 400 }
    )
  }

  let event: Stripe.Event

  try {
    // Temporarily disable signature verification for testing
    if (env.STRIPE_WEBHOOK_SECRET === 'whsec_your_webhook_secret') {
      // console.log('⚠️  Using test mode - webhook signature verification disabled')
      event = JSON.parse(body)
      // console.log('📦 Event type:', event.type, 'Event ID:', event.id)
    } else {
      // console.log('🔐 Verifying webhook signature...')
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        env.STRIPE_WEBHOOK_SECRET
      )
      // console.log('✅ Webhook signature verified. Event type:', event.type, 'Event ID:', event.id)
    }
  } catch (error) {
    console.error('❌ Webhook signature verification failed:', error)
    return NextResponse.json(
      { error: 'Invalid signature' },
      { status: 400 }
    )
  }

  // Webhook event logging removed - processing events directly

  try {
    // Process the event
    console.log(`🔄 Received event: ${event.type} (ID: ${event.id})`)

    // Only process specific event types and check for idempotency
    const supportedEvents = [
      'customer.subscription.created',
      'customer.subscription.updated',
      'customer.subscription.deleted',
      'invoice.payment_succeeded',
      'invoice.payment_failed'
    ]

    if (!supportedEvents.includes(event.type)) {
      // console.log(`⚠️ Unhandled event type: ${event.type}`)
      return NextResponse.json({ received: true })
    }

    // Check idempotency - skip if already processed
    const shouldProcess = await checkAndMarkEventProcessed(event.id, event.type)
    if (!shouldProcess) {
      return NextResponse.json({ received: true, status: 'already_processed' })
    }

    // Process the event based on type
    switch (event.type) {
      case 'customer.subscription.created':
        console.log('📝 Handling subscription created')
        const createdSubscription = event.data.object as Stripe.Subscription
        await handleSubscriptionChange(createdSubscription)
        console.log('✅ Subscription created handled successfully')
        break

      case 'customer.subscription.updated':
        console.log('📝 Handling subscription updated')
        const updatedSubscription = event.data.object as Stripe.Subscription
        const previousAttributes = event.data.previous_attributes as Partial<Stripe.Subscription>

        // Check if this is a cancellation at period end
        if (updatedSubscription.cancel_at_period_end && !previousAttributes?.cancel_at_period_end) {
          console.log('📅 Subscription cancellation scheduled')
          await handleSubscriptionCancelScheduled(updatedSubscription)
        }

        // Always update the subscription record
        await handleSubscriptionChange(updatedSubscription)
        console.log('✅ Subscription updated handled successfully')
        break

      case 'customer.subscription.deleted':
        console.log('📝 Handling subscription deleted')
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
        console.log('✅ Subscription deleted handled successfully')
        break

      case 'invoice.payment_succeeded':
        console.log('📝 Handling invoice payment succeeded')
        const successInvoice = event.data.object as Stripe.Invoice
        console.log('💰 Invoice details:', {
          id: successInvoice.id,
          amount_paid: successInvoice.amount_paid,
        })
        await handlePaymentSucceeded(successInvoice)
        console.log('✅ Invoice payment succeeded handled successfully')
        break

      case 'invoice.payment_failed':
        console.log('📝 Handling invoice payment failed')
        await handlePaymentFailed(event.data.object as Stripe.Invoice)
        console.log('✅ Invoice payment failed handled successfully')
        break
    }

    // Mark event as successfully processed
    await markEventProcessed(event.id)

    return NextResponse.json({ received: true, status: 'processed' })
  } catch (error) {
    console.error('❌ Error processing webhook:', error)
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    )
  }
}

// Add a GET endpoint to test webhook connectivity
export async function GET() {
  console.log('🔔 Webhook endpoint GET request received at:', new Date().toISOString())
  return NextResponse.json({
    message: 'Webhook endpoint is reachable',
    timestamp: new Date().toISOString(),
    webhookSecretConfigured: env.STRIPE_WEBHOOK_SECRET !== 'whsec_your_stripe_webhook_secret'
  })
}
