'use client'

import React from 'react'
import { CheckCircle, Clock, AlertCircle, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

export interface AnalysisProgressProps {
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress?: number
  fileName?: string
  estimatedTime?: string
  error?: string
  className?: string
}

export function AnalysisProgress({
  status,
  progress = 0,
  fileName,
  estimatedTime,
  error,
  className
}: AnalysisProgressProps) {
  const getStatusIcon = () => {
    switch (status) {
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />
      case 'processing':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'failed':
        return <AlertCircle className="w-5 h-5 text-red-500" />
      default:
        return <Clock className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusText = () => {
    switch (status) {
      case 'pending':
        return 'Analysis queued'
      case 'processing':
        return 'Analyzing document...'
      case 'completed':
        return 'Analysis completed'
      case 'failed':
        return 'Analysis failed'
      default:
        return 'Unknown status'
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600'
      case 'processing':
        return 'text-blue-600'
      case 'completed':
        return 'text-green-600'
      case 'failed':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getProgressBarColor = () => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-500'
      case 'processing':
        return 'bg-blue-500'
      case 'completed':
        return 'bg-green-500'
      case 'failed':
        return 'bg-red-500'
      default:
        return 'bg-gray-400'
    }
  }

  return (
    <div className={cn('w-full p-6 bg-white rounded-lg border', className)}>
      {/* Header */}
      <div className="flex items-center space-x-3 mb-4">
        {getStatusIcon()}
        <div className="flex-1">
          <h3 className={cn('font-medium', getStatusColor())}>
            {getStatusText()}
          </h3>
          {fileName && (
            <p className="text-sm text-gray-500 mt-1">
              File: {fileName}
            </p>
          )}
        </div>
      </div>

      {/* Progress Bar */}
      {(status === 'processing' || status === 'completed') && (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-600">Progress</span>
            <span className="text-sm font-medium text-gray-900">
              {Math.floor(progress)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={cn(
                'h-2 rounded-full transition-all duration-300 ease-out',
                getProgressBarColor()
              )}
              style={{ width: `${Math.min(progress, 100)}%` }}
            />
          </div>
        </div>
      )}

      {/* Status Details */}
      <div className="space-y-2">
        {status === 'pending' && (
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Status:</span>
            <span className="text-yellow-600">Waiting in queue</span>
          </div>
        )}

        {status === 'processing' && (
          <>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Status:</span>
              <span className="text-blue-600">Processing document</span>
            </div>
            {estimatedTime && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Estimated time:</span>
                <span className="text-gray-900">{estimatedTime}</span>
              </div>
            )}
          </>
        )}

        {status === 'completed' && (
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Status:</span>
            <span className="text-green-600">Ready for download</span>
          </div>
        )}

        {status === 'failed' && error && (
          <div className="mt-3 p-3 bg-red-50 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-red-800">Error Details</p>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Processing Steps (for processing status) */}
      {status === 'processing' && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Processing Steps</h4>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm text-gray-600">File uploaded and validated</span>
            </div>
            <div className="flex items-center space-x-2">
              {progress > 25 ? (
                <CheckCircle className="w-4 h-4 text-green-500" />
              ) : (
                <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
              )}
              <span className="text-sm text-gray-600">Extracting document content</span>
            </div>
            <div className="flex items-center space-x-2">
              {progress > 50 ? (
                <CheckCircle className="w-4 h-4 text-green-500" />
              ) : progress > 25 ? (
                <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
              ) : (
                <Clock className="w-4 h-4 text-gray-400" />
              )}
              <span className="text-sm text-gray-600">Analyzing document structure</span>
            </div>
            <div className="flex items-center space-x-2">
              {progress > 75 ? (
                <CheckCircle className="w-4 h-4 text-green-500" />
              ) : progress > 50 ? (
                <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
              ) : (
                <Clock className="w-4 h-4 text-gray-400" />
              )}
              <span className="text-sm text-gray-600">Generating insights</span>
            </div>
            <div className="flex items-center space-x-2">
              {progress >= 100 ? (
                <CheckCircle className="w-4 h-4 text-green-500" />
              ) : progress > 75 ? (
                <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
              ) : (
                <Clock className="w-4 h-4 text-gray-400" />
              )}
              <span className="text-sm text-gray-600">Finalizing report</span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
