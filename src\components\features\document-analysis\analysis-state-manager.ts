/**
 * Analysis State Management Utilities
 * 
 * This module handles state persistence, cleanup, and restoration
 * for document analysis operations.
 */

export interface AnalysisState {
  currentOperationId?: string;
  isProcessing?: boolean;
  analysisProgress?: number;
  selectedUseCase?: string;
  selectedSections?: string[];
  language?: string;
  customLanguage?: string;
  reportLength?: string;
  uploadedFileName?: string;
  uploadedFileSize?: number;
  timestamp?: number;
}

const STORAGE_KEY = 'documentAnalysisState';
const STATE_EXPIRY_MS = 10 * 60 * 1000; // 10 minutes

/**
 * Save analysis state to localStorage
 */
export const saveAnalysisState = (state: AnalysisState): void => {
  try {
    const stateWithTimestamp = {
      ...state,
      timestamp: Date.now()
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(stateWithTimestamp));
  } catch (error) {
    console.error('Failed to save analysis state:', error);
  }
};

/**
 * Load analysis state from localStorage
 */
export const loadAnalysisState = (): AnalysisState | null => {
  try {
    const savedState = localStorage.getItem(STORAGE_KEY);
    if (!savedState) return null;

    const state = JSON.parse(savedState) as AnalysisState;
    const now = Date.now();

    // Check if state has expired
    if (state.timestamp && (now - state.timestamp) > STATE_EXPIRY_MS) {
      clearAnalysisState();
      return null;
    }

    return state;
  } catch (error) {
    console.error('Failed to load analysis state:', error);
    clearAnalysisState();
    return null;
  }
};

/**
 * Clear analysis state from localStorage
 */
export const clearAnalysisState = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.error('Failed to clear analysis state:', error);
  }
};

/**
 * Create a placeholder file object from saved state
 */
export const createPlaceholderFile = (fileName: string, fileSize: number = 0): File => {
  const placeholderFile = new File([''], fileName, { type: 'application/pdf' });
  Object.defineProperty(placeholderFile, 'size', { value: fileSize });
  return placeholderFile;
};

/**
 * Check operation status from API
 */
export const checkOperationStatus = async (operationId: string): Promise<{
  success: boolean;
  data?: any;
  error?: string;
}> => {
  try {
    const response = await fetch(`/api/analyze/${operationId}`);
    if (response.ok) {
      const data = await response.json();
      return { success: true, data };
    } else {
      return { success: false, error: 'Failed to fetch operation status' };
    }
  } catch (error) {
    console.error('Error checking operation status:', error);
    return { success: false, error: 'Network error' };
  }
};

/**
 * Cleanup analysis resources
 */
export const cleanupAnalysisResources = (
  eventSource: EventSource | null,
  progressTimer: NodeJS.Timeout | null
): void => {
  // Clean up SSE connection
  if (eventSource) {
    console.log('Closing SSE connection');
    if (eventSource.readyState !== EventSource.CLOSED) {
      eventSource.close();
    }
  }

  // Clean up progress timer
  if (progressTimer) {
    console.log('Clearing progress timer');
    clearInterval(progressTimer);
  }

  // Clear saved state
  clearAnalysisState();
};

/**
 * Restore form state from saved analysis state
 */
export const restoreFormState = (
  state: AnalysisState,
  setters: {
    setSelectedUseCase: (value: string | null) => void;
    setSelectedSections: (value: string[]) => void;
    setLanguage: (value: string) => void;
    setCustomLanguage: (value: string) => void;
    setReportLength: (value: string) => void;
    setUploadedFile: (value: File | null) => void;
  }
): void => {
  const {
    setSelectedUseCase,
    setSelectedSections,
    setLanguage,
    setCustomLanguage,
    setReportLength,
    setUploadedFile
  } = setters;

  // Restore form selections
  if (state.selectedUseCase) {
    setSelectedUseCase(state.selectedUseCase);
  }
  if (state.selectedSections) {
    setSelectedSections(state.selectedSections);
  }
  if (state.language) {
    setLanguage(state.language);
  }
  if (state.customLanguage) {
    setCustomLanguage(state.customLanguage);
  }
  if (state.reportLength) {
    setReportLength(state.reportLength);
  }

  // Restore file info if available (but not if it was processing)
  if (state.uploadedFileName && !state.isProcessing) {
    const placeholderFile = createPlaceholderFile(
      state.uploadedFileName,
      state.uploadedFileSize || 0
    );
    setUploadedFile(placeholderFile);
  }
};

/**
 * Handle analysis cancellation
 */
export const cancelAnalysis = (
  eventSource: EventSource | null,
  progressTimer: NodeJS.Timeout | null,
  setters: {
    setIsProcessing: (value: boolean) => void;
    setAnalysisProgress: (value: number) => void;
    setCurrentOperationId: (value: string | null) => void;
    setEventSource: (value: EventSource | null) => void;
    setProgressTimer: (value: NodeJS.Timeout | null) => void;
    setIsProgressTimerActive: (value: boolean) => void;
    setErrorMessage: (value: string) => void;
  }
): void => {
  console.log('Cancelling analysis...');

  const {
    setIsProcessing,
    setAnalysisProgress,
    setCurrentOperationId,
    setEventSource,
    setProgressTimer,
    setIsProgressTimerActive,
    setErrorMessage
  } = setters;

  // Clean up resources
  cleanupAnalysisResources(eventSource, progressTimer);

  // Reset state
  setIsProcessing(false);
  setAnalysisProgress(0);
  setCurrentOperationId(null);
  setEventSource(null);
  setProgressTimer(null);
  setIsProgressTimerActive(false);

  // Show cancellation message
  setErrorMessage('Analysis cancelled. Note: The credit spent cannot be refunded.');
};
