import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

// POST /api/templates/[id]/usage
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    const userId = (session?.user as any)?.id
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: templateId } = await params

    // Check if template exists and belongs to user
    const template = await prisma.documentAnalysisTemplate.findFirst({
      where: {
        id: templateId,
        userId
      }
    })

    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 })
    }

    // Increment usage count
    await prisma.documentAnalysisTemplate.update({
      where: {
        id: templateId
      },
      data: {
        usageCount: {
          increment: 1
        }
      }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error incrementing template usage:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
