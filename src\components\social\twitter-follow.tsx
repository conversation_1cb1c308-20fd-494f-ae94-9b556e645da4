'use client'

import { useEffect, useState } from 'react'

interface XFollowProps {
  username?: string
  size?: 'large' | 'medium'
  showCount?: boolean
  showUsername?: boolean
  className?: string
}

export function XFollow({
  username = 'DocuChampAI',
  size = 'medium',
  showCount = false,
  showUsername = true,
  className = ''
}: XFollowProps) {
  const handleFollow = () => {
    window.open(`https://twitter.com/${username}`, '_blank', 'noopener,noreferrer')
  }

  return (
    <div className={className}>
      <button
        onClick={handleFollow}
        className="group relative w-full max-w-xs overflow-hidden rounded-lg bg-gradient-to-r from-gray-800 to-black px-4 py-2 text-white shadow-md transition-all duration-300 hover:from-gray-900 hover:to-gray-800 hover:shadow-lg hover:scale-105 hover:-translate-y-0.5 active:scale-95 cursor-pointer"
        title={`Follow @${username} on X`}
      >
        <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        <div className="relative flex items-center justify-center space-x-2">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
          </svg>
          <span className="font-medium text-sm">Follow on X</span>
        </div>
      </button>
    </div>
  )
}

interface XShareProps {
  url?: string
  text?: string
  hashtags?: string
  via?: string
  size?: 'large' | 'medium'
  className?: string
}

export function XShare({
  url,
  text = 'Check out DocuChampAI - AI-powered document analysis!',
  hashtags = 'AI,DocumentAnalysis,Automation',
  via = 'DocuChampAI',
  size = 'medium',
  className = ''
}: XShareProps) {
  const [currentUrl, setCurrentUrl] = useState('')

  useEffect(() => {
    setCurrentUrl(url || window.location.href)
  }, [url])
  useEffect(() => {
    // Load Twitter widgets script
    if (typeof window !== 'undefined' && !window.twttr) {
      const script = document.createElement('script')
      script.src = 'https://platform.twitter.com/widgets.js'
      script.async = true
      script.charset = 'utf-8'
      document.body.appendChild(script)
    } else if (window.twttr && window.twttr.widgets) {
      // Re-load widgets if script already loaded
      window.twttr.widgets.load()
    }
  }, [])

  if (!currentUrl) {
    return <div className={className}>Loading...</div>
  }

  return (
    <div className={className}>
      <a
        href="https://twitter.com/share?ref_src=twsrc%5Etfw"
        className="twitter-share-button"
        data-url={currentUrl}
        data-text={text}
        data-hashtags={hashtags}
        data-via={via}
        data-size={size}
      >
        Post on X
      </a>
    </div>
  )
}

// Declare Twitter widgets types
declare global {
  interface Window {
    twttr: {
      widgets: {
        load: () => void
      }
    }
  }
}
