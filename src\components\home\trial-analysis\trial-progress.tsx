'use client'

import React from 'react'
import { CheckCircle, Clock, FileText, Zap } from 'lucide-react'
import { cn } from '@/lib/utils'

interface TrialProgressProps {
  status: 'idle' | 'processing' | 'completed' | 'error'
  progress: number
  fileName?: string
  estimatedTime?: string
  currentStep?: string
}

const progressSteps = [
  { id: 'upload', label: 'File Upload', icon: FileText },
  { id: 'processing', label: 'AI Analysis', icon: Zap },
  { id: 'complete', label: 'Results Ready', icon: CheckCircle }
]

export function TrialProgress({
  status,
  progress,
  fileName,
  estimatedTime,
  currentStep = 'processing'
}: TrialProgressProps) {
  if (status === 'idle') {
    return null
  }

  const getCurrentStepIndex = () => {
    if (status === 'completed') return 2
    if (progress > 80) return 2
    if (progress > 20) return 1
    return 0
  }

  const currentStepIndex = getCurrentStepIndex()

  return (
    <div className="bg-white border-2 border-purple-200 rounded-xl p-6">
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          {status === 'processing' ? 'Analyzing Your Document...' : 
           status === 'completed' ? 'Analysis Complete!' : 
           status === 'error' ? 'Analysis Failed' : 'Processing'}
        </h3>
        {fileName && (
          <p className="text-gray-600">
            <span className="font-medium">{fileName}</span>
            {estimatedTime && status === 'processing' && (
              <span className="text-sm text-gray-500 ml-2">
                • Est. {estimatedTime}
              </span>
            )}
          </p>
        )}
      </div>

      {/* Progress Steps */}
      <div className="relative flex items-center justify-between mb-6">
        {progressSteps.map((step, index) => {
          const Icon = step.icon
          const isActive = index <= currentStepIndex
          const isCurrent = index === currentStepIndex && status === 'processing'
          const isCompleted = index < currentStepIndex || status === 'completed'

          return (
            <React.Fragment key={step.id}>
              <div className="flex flex-col items-center z-10 bg-white">
                <div
                  className={cn(
                    'w-12 h-12 rounded-full flex items-center justify-center mb-2 transition-all duration-300',
                    isCompleted
                      ? 'bg-green-500 text-white'
                      : isCurrent
                      ? 'bg-purple-500 text-white animate-pulse'
                      : isActive
                      ? 'bg-purple-100 text-purple-600'
                      : 'bg-gray-100 text-gray-400'
                  )}
                >
                  <Icon className="w-5 h-5" />
                </div>
                <span
                  className={cn(
                    'text-sm font-medium text-center',
                    isActive ? 'text-gray-900' : 'text-gray-500'
                  )}
                >
                  {step.label}
                </span>
              </div>
              {index < progressSteps.length - 1 && (
                <div className="flex-1 px-4">
                  <div
                    className={cn(
                      'h-0.5 w-full transition-all duration-300',
                      isCompleted ? 'bg-green-500' : 'bg-gray-200'
                    )}
                  />
                </div>
              )}
            </React.Fragment>
          )
        })}
      </div>

      {/* Progress Bar */}
      {status === 'processing' && (
        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${Math.min(progress, 99)}%` }}
            />
          </div>
        </div>
      )}

      {/* Status Messages */}
      <div className="text-center">
        {status === 'processing' && (
          <div className="flex items-center justify-center space-x-2 text-purple-600">
            <Clock className="w-4 h-4 animate-spin" />
            <span className="text-sm font-medium">
              {currentStep === 'upload' ? 'Uploading file...' :
               currentStep === 'processing' ? 'AI is analyzing your document...' :
               'Finalizing results...'}
            </span>
          </div>
        )}
        
        {status === 'completed' && (
          <div className="flex items-center justify-center space-x-2 text-green-600">
            <CheckCircle className="w-5 h-5" />
            <span className="font-medium">Your analysis is ready!</span>
          </div>
        )}
        
        {status === 'error' && (
          <div className="flex items-center justify-center space-x-2 text-red-600">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="font-medium">Analysis failed. Please try again.</span>
          </div>
        )}
      </div>

      {/* Trial Info */}
      {status === 'processing' && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-xs text-yellow-800 text-center">
            🎯 This is a free trial analysis. Sign up for advanced AI features!
          </p>
        </div>
      )}
    </div>
  )
}
