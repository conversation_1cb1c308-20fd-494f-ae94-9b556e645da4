/**
 * Analysis job management utilities
 */

import fs from 'fs'
import path from 'path'

export interface AnalysisJob {
  id: string
  userId: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  result?: {
    metadata: any
    markdownContent: string
    docxUrl: string
  }
  error?: string
  createdAt: Date
  updatedAt: Date
}

export interface JobUpdateData {
  status?: AnalysisJob['status']
  progress?: number
  result?: AnalysisJob['result']
  error?: string
}

// File-based storage for development (in production, use Redis or database)
const JOBS_DIR = path.join(process.cwd(), '.next', 'analysis-jobs')

// In-memory storage for quick access
const analysisJobs = new Map<string, AnalysisJob>()

/**
 * Ensure jobs directory exists
 */
export function ensureJobsDirectory(): void {
  if (!fs.existsSync(JOBS_DIR)) {
    fs.mkdirSync(JOBS_DIR, { recursive: true })
  }
}

/**
 * Get job file path
 */
function getJobFilePath(jobId: string): string {
  return path.join(JOBS_DIR, `${jobId}.json`)
}

/**
 * Save job to file system
 */
export function saveJob(job: AnalysisJob): void {
  try {
    ensureJobsDirectory()
    const filePath = getJobFilePath(job.id)
    fs.writeFileSync(filePath, JSON.stringify(job, null, 2))
  } catch (error) {
    console.error('Error saving job:', error)
  }
}

/**
 * Load job from file system
 */
export function loadJob(jobId: string): AnalysisJob | null {
  try {
    const filePath = getJobFilePath(jobId)
    if (!fs.existsSync(filePath)) {
      return null
    }
    
    const data = fs.readFileSync(filePath, 'utf-8')
    const job = JSON.parse(data)
    
    // Convert date strings back to Date objects
    job.createdAt = new Date(job.createdAt)
    job.updatedAt = new Date(job.updatedAt)
    
    return job
  } catch (error) {
    console.error('Error loading job:', error)
    return null
  }
}

/**
 * Get job from memory or file system
 */
export function getJob(jobId: string): AnalysisJob | null {
  // Try memory first
  const memoryJob = analysisJobs.get(jobId)
  if (memoryJob) {
    return memoryJob
  }
  
  // Fall back to file system
  const fileJob = loadJob(jobId)
  if (fileJob) {
    // Cache in memory for future access
    analysisJobs.set(jobId, fileJob)
    return fileJob
  }
  
  return null
}

/**
 * Create new analysis job
 */
export function createJob(jobId: string, userId: string): AnalysisJob {
  const job: AnalysisJob = {
    id: jobId,
    userId,
    status: 'pending',
    progress: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  }
  
  // Save to both memory and file
  analysisJobs.set(jobId, job)
  saveJob(job)
  
  return job
}

/**
 * Update existing job
 */
export function updateJob(jobId: string, updates: JobUpdateData): AnalysisJob | null {
  const job = getJob(jobId)
  if (!job) {
    return null
  }
  
  // Apply updates
  if (updates.status !== undefined) job.status = updates.status
  if (updates.progress !== undefined) job.progress = updates.progress
  if (updates.result !== undefined) job.result = updates.result
  if (updates.error !== undefined) job.error = updates.error
  
  job.updatedAt = new Date()
  
  // Save to both memory and file
  analysisJobs.set(jobId, job)
  saveJob(job)
  
  return job
}

/**
 * Delete job
 */
export function deleteJob(jobId: string): boolean {
  try {
    // Remove from memory
    analysisJobs.delete(jobId)
    
    // Remove from file system
    const filePath = getJobFilePath(jobId)
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath)
    }
    
    return true
  } catch (error) {
    console.error('Error deleting job:', error)
    return false
  }
}

/**
 * Get all jobs for a user
 */
export function getUserJobs(userId: string): AnalysisJob[] {
  try {
    ensureJobsDirectory()
    const files = fs.readdirSync(JOBS_DIR)
    const jobs: AnalysisJob[] = []
    
    for (const file of files) {
      if (file.endsWith('.json')) {
        const jobId = file.replace('.json', '')
        const job = getJob(jobId)
        if (job && job.userId === userId) {
          jobs.push(job)
        }
      }
    }
    
    // Sort by creation date (newest first)
    return jobs.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
  } catch (error) {
    console.error('Error getting user jobs:', error)
    return []
  }
}

/**
 * Clean up old completed jobs (older than 24 hours)
 */
export function cleanupOldJobs(): number {
  try {
    ensureJobsDirectory()
    const files = fs.readdirSync(JOBS_DIR)
    let deletedCount = 0
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000) // 24 hours ago
    
    for (const file of files) {
      if (file.endsWith('.json')) {
        const jobId = file.replace('.json', '')
        const job = loadJob(jobId)
        
        if (job && 
            (job.status === 'completed' || job.status === 'failed') &&
            job.updatedAt.getTime() < cutoffTime) {
          deleteJob(jobId)
          deletedCount++
        }
      }
    }
    
    return deletedCount
  } catch (error) {
    console.error('Error cleaning up old jobs:', error)
    return 0
  }
}

/**
 * Get job statistics
 */
export function getJobStats(): {
  total: number
  pending: number
  processing: number
  completed: number
  failed: number
} {
  try {
    ensureJobsDirectory()
    const files = fs.readdirSync(JOBS_DIR)
    const stats = {
      total: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0
    }
    
    for (const file of files) {
      if (file.endsWith('.json')) {
        const jobId = file.replace('.json', '')
        const job = loadJob(jobId)
        
        if (job) {
          stats.total++
          stats[job.status]++
        }
      }
    }
    
    return stats
  } catch (error) {
    console.error('Error getting job stats:', error)
    return {
      total: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0
    }
  }
}
