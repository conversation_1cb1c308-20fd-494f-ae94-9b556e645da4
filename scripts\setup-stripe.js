#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('🚀 Stripe Setup Helper')
console.log('======================')

// Check if .env file exists
const envPath = path.join(process.cwd(), '.env')
if (!fs.existsSync(envPath)) {
  console.error('❌ .env file not found!')
  console.log('Please create a .env file first.')
  process.exit(1)
}

// Read .env file
const envContent = fs.readFileSync(envPath, 'utf8')

// Check for Stripe keys
const hasPublishableKey = envContent.includes('STRIPE_PUBLISHABLE_KEY=pk_')
const hasSecretKey = envContent.includes('STRIPE_SECRET_KEY=sk_')
const hasWebhookSecret = envContent.includes('STRIPE_WEBHOOK_SECRET=whsec_')

console.log('\n📋 Stripe Configuration Status:')
console.log(`✅ Publishable Key: ${hasPublishableKey ? 'Found' : '❌ Missing'}`)
console.log(`✅ Secret Key: ${hasSecretKey ? 'Found' : '❌ Missing'}`)
console.log(`✅ Webhook Secret: ${hasWebhookSecret ? 'Found' : '❌ Missing'}`)

if (!hasPublishableKey || !hasSecretKey) {
  console.log('\n🔑 Missing Stripe API Keys!')
  console.log('Please add your Stripe keys to .env file:')
  console.log('STRIPE_PUBLISHABLE_KEY=pk_test_...')
  console.log('STRIPE_SECRET_KEY=sk_test_...')
  console.log('\nGet them from: https://dashboard.stripe.com/test/apikeys')
}

if (!hasWebhookSecret) {
  console.log('\n🔗 Missing Webhook Secret!')
  console.log('For LOCAL DEVELOPMENT, run Stripe CLI:')
  console.log('stripe listen --forward-to localhost:3000/api/webhooks/stripe')
  console.log('Copy the webhook secret (whsec_...) and add to .env file')
  console.log('Note: No need to create webhook endpoint in Stripe Dashboard for local dev!')
}

// Check config file
const configPath = path.join(process.cwd(), 'config', 'app.config.json')
if (fs.existsSync(configPath)) {
  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'))
  const creditPackages = config.billing?.creditPackages || []
  
  console.log('\n💳 Credit Packages Configuration:')
  creditPackages.forEach((pkg, index) => {
    const hasRealPriceId = pkg.stripePriceId && pkg.stripePriceId.startsWith('price_') && !pkg.stripePriceId.includes('YourActual')
    console.log(`${index + 1}. ${pkg.name}: ${hasRealPriceId ? '✅' : '❌'} ${pkg.stripePriceId}`)
  })
  
  const needsPriceIds = creditPackages.some(pkg => !pkg.stripePriceId || pkg.stripePriceId.includes('YourActual'))
  if (needsPriceIds) {
    console.log('\n🏷️  You need to create Stripe products and update Price IDs!')
    console.log('1. Go to https://dashboard.stripe.com/test/products')
    console.log('2. Create product: "AI Stock Selector Credits"')
    console.log('3. Add these monthly prices:')
    creditPackages.forEach(pkg => {
      console.log(`   - ${pkg.name}: HKD $${pkg.price}/month`)
    })
    console.log('4. Copy the Price IDs and update config/app.config.json')
  }
}

console.log('\n🎯 Next Steps:')
if (!hasPublishableKey || !hasSecretKey) {
  console.log('1. ✅ Add Stripe API keys to .env')
} else {
  console.log('1. ✅ Stripe API keys configured')
}

if (!hasWebhookSecret) {
  console.log('2. ❌ Set up webhook secret')
} else {
  console.log('2. ✅ Webhook secret configured')
}

console.log('3. ❌ Create Stripe products and update Price IDs')
console.log('4. ❌ Test payment flow')

console.log('\n📚 For LOCAL DEVELOPMENT:')
console.log('Use Stripe CLI - no webhook setup needed in dashboard!')
console.log('stripe listen --forward-to localhost:3000/api/webhooks/stripe')
console.log('\n📚 For PRODUCTION:')
console.log('Create webhook endpoint in Stripe Dashboard with these events:')
console.log('- customer.subscription.created')
console.log('- customer.subscription.updated')
console.log('- customer.subscription.deleted')
console.log('- invoice.payment_succeeded')
console.log('- invoice.payment_failed')
console.log('- checkout.session.completed')

console.log('\n🧪 Test Commands:')
console.log('npm run dev          # Start app with Stripe webhook listener')
console.log('npm run dev:next     # Start app only (without Stripe CLI)')
console.log('npm run stripe:listen # Start Stripe webhook listener only')

console.log('\n💡 New User Bonus: 10 free credits automatically added!')
