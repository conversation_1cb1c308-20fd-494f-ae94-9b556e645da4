'use client'

import { Facebook } from 'lucide-react'

interface FacebookFollowProps {
  pageUrl?: string
  className?: string
}

export function FacebookFollow({
  pageUrl = 'https://www.facebook.com/docuchampai',
  className = ''
}: FacebookFollowProps) {
  const handleFollow = () => {
    window.open(pageUrl, '_blank', 'noopener,noreferrer')
  }

  return (
    <div className={className}>
      <button
        onClick={handleFollow}
        className="group relative w-full max-w-xs overflow-hidden rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-2 text-white shadow-md transition-all duration-300 hover:from-blue-600 hover:to-blue-700 hover:shadow-lg hover:scale-105 hover:-translate-y-0.5 active:scale-95 cursor-pointer"
        title="Follow us on Facebook"
      >
        <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        <div className="relative flex items-center justify-center space-x-2">
          <Facebook className="w-4 h-4" />
          <span className="font-medium text-sm">Follow on Facebook</span>
        </div>
      </button>
    </div>
  )
}

// Note: This is a simple follow button that opens Facebook in a new tab
// Facebook does have official Page plugins, but they're larger and may not load consistently
// This approach ensures the button always appears for better UX
