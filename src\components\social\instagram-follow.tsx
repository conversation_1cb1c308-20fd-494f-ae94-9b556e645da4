'use client'

import { Instagram } from 'lucide-react'

interface InstagramFollowProps {
  username?: string
  className?: string
}

export function InstagramFollow({
  username = 'docuchampai',
  className = ''
}: InstagramFollowProps) {
  const handleFollow = () => {
    window.open(`https://www.instagram.com/${username}/`, '_blank', 'noopener,noreferrer')
  }

  return (
    <div className={className}>
      <button
        onClick={handleFollow}
        className="group relative w-full max-w-xs overflow-hidden rounded-lg bg-gradient-to-r from-pink-500 to-purple-600 px-4 py-2 text-white shadow-md transition-all duration-300 hover:from-pink-600 hover:to-purple-700 hover:shadow-lg hover:scale-105 hover:-translate-y-0.5 active:scale-95 cursor-pointer"
        title={`Follow @${username} on Instagram`}
      >
        <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        <div className="relative flex items-center justify-center space-x-2">
          <Instagram className="w-4 h-4" />
          <span className="font-medium text-sm">Follow @{username}</span>
        </div>
      </button>
    </div>
  )
}

// Note: Instagram doesn't provide official embed widgets like Facebook and X (Twitter)
// This is a custom button that opens Instagram in a new tab
// Instagram does have an "Instagram Basic Display API" but it's for displaying posts, not follow buttons
// For SEO purposes, this custom button with proper schema markup is the best approach
