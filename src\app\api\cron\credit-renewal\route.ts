import { NextResponse } from 'next/server'
import { processAllCreditRenewals } from '@/lib/credit-renewal'
import { env } from '@/lib/config'

export async function GET() {
  try {
    // Check if credit management is enabled
    if (!env.ENABLE_CREDIT_MANAGEMENT) {
      return NextResponse.json({
        success: false,
        message: 'Credit management is disabled',
        timestamp: new Date().toISOString(),
      })
    }

    console.log('🔄 Starting hourly credit renewal check...')
    
    const results = await processAllCreditRenewals()
    
    console.log(`✅ Credit renewal check completed. Processed ${results.length} renewals.`)
    
    return NextResponse.json({
      success: true,
      message: `Processed ${results.length} credit renewals`,
      results: results.map(r => ({
        userId: r.userId,
        previousCredits: r.previousCredits,
        newCredits: r.newCredits,
        nextRenewalDate: r.nextRenewalDate,
      })),
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error('❌ Error in hourly credit renewal check:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}

export async function POST() {
  // Allow manual triggering via POST
  return GET()
}
