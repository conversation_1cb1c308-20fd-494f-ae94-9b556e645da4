'use client'

import { useEffect, useState } from 'react'

interface FacebookPageProps {
  pageUrl?: string
  width?: number
  height?: number
  showFacepile?: boolean
  showPosts?: boolean
  hideCover?: boolean
  smallHeader?: boolean
  adaptContainerWidth?: boolean
  className?: string
}

export function FacebookPage({
  pageUrl = 'https://www.facebook.com/docuchampai',
  width = 340,
  height = 500,
  showFacepile = true,
  showPosts = false,
  hideCover = false,
  smallHeader = true,
  adaptContainerWidth = true,
  className = ''
}: FacebookPageProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    
    // Load Facebook SDK
    if (typeof window !== 'undefined' && !window.FB) {
      const script = document.createElement('script')
      script.src = 'https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v18.0'
      script.async = true
      script.defer = true
      script.crossOrigin = 'anonymous'
      document.body.appendChild(script)

      // Initialize Facebook SDK when loaded
      script.onload = () => {
        if (window.FB) {
          window.FB.init({
            appId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID || '',
            xfbml: true,
            version: 'v18.0'
          })
        }
      }
    } else if (window.FB) {
      // Re-parse if SDK already loaded
      window.FB.XFBML.parse()
    }
  }, [])

  if (!mounted) {
    return <div className={className}>Loading Facebook page...</div>
  }

  return (
    <div className={className}>
      <div
        className="fb-page"
        data-href={pageUrl}
        data-tabs="timeline"
        data-width={width}
        data-height={height}
        data-small-header={smallHeader}
        data-adapt-container-width={adaptContainerWidth}
        data-hide-cover={hideCover}
        data-show-facepile={showFacepile}
        data-show-posts={showPosts}
      />
    </div>
  )
}

// Declare Facebook SDK types
declare global {
  interface Window {
    FB: {
      init: (params: any) => void
      XFBML: {
        parse: () => void
      }
    }
  }
}
