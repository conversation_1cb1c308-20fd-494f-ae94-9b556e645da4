'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Clock, Lightbulb, Search, TrendingUp } from 'lucide-react'
import Link from 'next/link'

interface ComingSoonProps {
  featureName: string
  description: string
  iconType?: 'lightbulb' | 'search' | 'trending'
  expectedDate?: string
}

export function ComingSoon({
  featureName,
  description,
  iconType = 'lightbulb',
  expectedDate = "Soon"
}: ComingSoonProps) {
  const getIcon = () => {
    switch (iconType) {
      case 'search':
        return Search
      case 'trending':
        return TrendingUp
      default:
        return Lightbulb
    }
  }

  const Icon = getIcon()
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      {/* Back Button */}
      <div className="max-w-4xl mx-auto pt-8 px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <Link href="/features/document-analysis" title="Go back to Document Analysis feature">
            <Button variant="outline" className="flex items-center space-x-2">
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Document Analysis</span>
            </Button>
          </Link>
        </div>
      </div>

      <div className="max-w-4xl mx-auto pb-16 px-4 sm:px-6 lg:px-8">
        {/* Coming Soon Content */}
        <div className="text-center">
          <div className="mx-auto w-24 h-24 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center mb-8">
            <Icon className="w-12 h-12 text-purple-600" />
          </div>
          
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {featureName}
          </h1>
          
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            {description}
          </p>

          <div className="bg-white rounded-lg shadow-sm border p-8 max-w-md mx-auto">
            <Clock className="w-16 h-16 text-purple-500 mx-auto mb-4" />
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              Coming Soon
            </h2>
            <p className="text-gray-600 mb-6">
              We&apos;re working hard to bring you this amazing feature. Stay tuned for updates!
            </p>
            <div className="text-sm text-purple-600 font-medium">
              Expected: {expectedDate}
            </div>
          </div>

          {/* Available Now */}
          <div className="mt-12">
            <p className="text-gray-600 mb-4">
              In the meantime, try our available feature:
            </p>
            <Link href="/features/document-analysis" title="Try our Document Analysis feature">
              <Button className="text-white transition-all duration-300 transform hover:scale-105 !rounded-md bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 shadow-lg hover:shadow-purple-500/50 !rounded-md hover:!rounded-md px-8 py-3">
                Document Analysis
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
