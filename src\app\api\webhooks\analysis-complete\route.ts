import { NextRequest, NextResponse } from 'next/server'
import { updateUsageRecord, getUsageRecordByOperationId } from '@/lib/db'
import { env } from '@/lib/config'
import { notifyOperation } from '@/app/api/analyze/[operationId]/stream/route'

// Verify webhook authentication using Bearer token
function verifyWebhookAuth(authHeader: string | null): boolean {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return false
  }

  const token = authHeader.substring(7) // Remove 'Bearer ' prefix
  return token === env.WEBHOOK_ANALYSIS_COMPLETE_KEY
}

// POST /api/webhooks/analysis-complete
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')

    // Verify webhook authentication
    if (!verifyWebhookAuth(authHeader)) {
      console.error('Invalid webhook authentication')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const data = await request.json()
    const { operationId, userId, success, markdownContent, error, metadata, output_file_blob_sas_url } = data

    // Check if this is a trial operation
    const isTrial = operationId.startsWith('trial_')

    // For trial operations, userId can be undefined; for regular operations, userId is required
    if (!operationId || success === undefined || (!isTrial && !userId)) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }
    let usageRecord = null
    let actualUserId = userId

    if (!isTrial) {
      // For non-trial operations, get the usage record to verify it exists
      usageRecord = await getUsageRecordByOperationId(operationId)
      if (!usageRecord) {
        console.error(`Usage record not found for operation ${operationId}`)
        return NextResponse.json({ error: 'Operation not found' }, { status: 404 })
      }

      // Check if userId matches - if not, use the one from database (Azure Function bug)
      actualUserId = usageRecord.userId
      if (usageRecord.userId !== userId) {
        console.error(`🐛 Azure Function Bug: User ID mismatch for operation ${operationId}`)
        console.error(`  Expected (from DB): ${usageRecord.userId}`)
        console.error(`  Received (from webhook): ${userId}`)
        console.error(`  🔍 Azure Function is sending operationId as userId!`)

        // Use the correct userId from the database record
        actualUserId = usageRecord.userId
        console.warn(`✅ Using correct userId from database: ${actualUserId}`)
      }

      // Update the usage record based on success status
      const updateData: any = {
        status: success ? 'completed' : 'failed',
        completedAt: new Date(),
        metadata: {
          ...(usageRecord.metadata as any),
          ...metadata,
          completedAt: new Date().toISOString()
        }
      }

      if (success && output_file_blob_sas_url) {
        updateData.fileLink = output_file_blob_sas_url
        console.log(`✅ Analysis completed successfully for operation ${operationId}`)
      }

      // Store markdown content if provided
      if (markdownContent) {
        updateData.metadata.markdownContent = markdownContent
      }

      if (!success && error) {
        updateData.metadata.error = error
        updateData.metadata.failedAt = new Date().toISOString()
        console.log(`❌ Analysis failed for operation ${operationId}: ${error}`)

        // TODO: Implement credit refund for failed analysis
        // This should refund the credit to the user since the analysis failed
      }

      // Update the usage record
      const updatedRecord = await updateUsageRecord(operationId, updateData)

      if (!updatedRecord) {
        console.error(`Failed to update usage record for operation ${operationId}`)
        return NextResponse.json({ error: 'Failed to update record' }, { status: 500 })
      }

      console.log(`Successfully updated usage record for operation ${operationId} with status ${success ? 'completed' : 'failed'}`)
    } else {
      // For trial operations, just log the completion
      console.log(`${success ? '✅' : '❌'} Trial analysis ${success ? 'completed' : 'failed'} for operation ${operationId}`)
    }

    // Send real-time notification via SSE
    try {
      const ssePayload = {
        success: success,
        markdownContent: markdownContent,
        output_file_blob_sas_url: output_file_blob_sas_url,
        metadata: metadata || {}
      }

      // For non-trial operations, use data from updated record if available
      if (!isTrial && usageRecord) {
        ssePayload.markdownContent = (usageRecord.metadata as any)?.markdownContent || markdownContent
        ssePayload.metadata = usageRecord.metadata || metadata || {}
      }

      console.log(`📡 Sending SSE notification for operation ${operationId}:`, {
        success: ssePayload.success,
        hasMarkdown: !!ssePayload.markdownContent,
        hasDocxUrl: !!ssePayload.output_file_blob_sas_url,
        markdownLength: ssePayload.markdownContent?.length || 0
      })

      notifyOperation(operationId, ssePayload)
      console.log(`✅ SSE notification sent successfully for operation ${operationId}`)
    } catch (error) {
      console.error(`❌ Failed to send SSE notification for operation ${operationId}:`, error)
      // Don't fail the webhook if SSE notification fails
    }

    return NextResponse.json({
      success: true,
      operationId,
      message: isTrial ? 'Trial analysis completed successfully' : 'Usage record updated successfully'
    })

  } catch (error: any) {
    console.error('Error processing analysis completion webhook:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 })
  }
}