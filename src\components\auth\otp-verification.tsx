'use client'

import { useState, useRef, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { signIn } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface OTPVerificationProps {
  userId: string
  email: string
  onSuccess?: () => void
  onBack?: () => void
}

export function OTPVerification({ userId, email, onSuccess, onBack }: OTPVerificationProps) {
  const [otp, setOtp] = useState(['', '', '', '', '', ''])
  const [isLoading, setIsLoading] = useState(false)
  const [isResending, setIsResending] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const [signingIn, setSigningIn] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const router = useRouter()
  const inputRefs = useRef<(HTMLInputElement | null)[]>([])

  // Countdown timer for resend button
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [countdown])

  // Focus first input on mount
  useEffect(() => {
    inputRefs.current[0]?.focus()
  }, [])

  const handleInputChange = (index: number, value: string) => {
    // Only allow digits
    if (!/^\d*$/.test(value)) return

    const newOtp = [...otp]
    newOtp[index] = value.slice(-1) // Only take the last character

    setOtp(newOtp)
    setError('')

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus()
    }

    // Auto-submit when all fields are filled
    if (newOtp.every(digit => digit !== '') && newOtp.join('').length === 6) {
      handleVerifyOTP(newOtp.join(''))
    }
  }

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus()
    }
  }

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault()
    const pastedData = e.clipboardData.getData('text').replace(/\D/g, '').slice(0, 6)
    
    if (pastedData.length === 6) {
      const newOtp = pastedData.split('')
      setOtp(newOtp)
      setError('')
      handleVerifyOTP(pastedData)
    }
  }

  const handleVerifyOTP = async (otpCode: string) => {
    setIsLoading(true)
    setError('')

    try {
      const response = await fetch('/api/auth/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, otp: otpCode }),
      })

      const data = await response.json()

      if (!response.ok) {
        if (data.code === 'OTP_EXPIRED') {
          setError('**Code expired.** Your verification code has expired after 15 minutes. Please request a new one below.')
        } else if (data.code === 'INVALID_OTP') {
          setError('**Invalid code.** Please check your verification code and try again.')
          // Clear the OTP inputs
          setOtp(['', '', '', '', '', ''])
          inputRefs.current[0]?.focus()
        } else {
          setError(`**Verification failed.** ${data.error || 'Please try again.'}`)
        }
      } else {
        setSuccess(true)

        // Check if we have a sign-in token for automatic login
        if (data.signInToken) {
          // Show green tick for 1.5 seconds before starting sign-in process
          setTimeout(async () => {
            setSigningIn(true)

            // Attempt automatic sign-in
            try {
              const signInResult = await signIn('credentials', {
                email: email,
                password: 'auto-signin-token:' + data.signInToken,
                redirect: false,
              })

              if (signInResult?.ok) {
                // Successful auto sign-in
                if (data.isNewUser) {
                  // Mark as new user and clear any existing tutorial state for fresh start
                  sessionStorage.setItem('isNewUser', 'true')
                  localStorage.removeItem('docuchampai_tutorial_state')
                  console.log('✅ New user verified - will show welcome message and tutorial')
                }

                setTimeout(() => {
                  onSuccess?.()
                  const redirectUrl = data.isNewUser
                    ? '/features/document-analysis?newUser=true'
                    : '/features/document-analysis'
                  router.push(redirectUrl)
                }, 1000)
              } else {
                // Auto sign-in failed, redirect to manual sign-in
                setTimeout(() => {
                  onSuccess?.()
                  router.push('/auth/signin?verified=true&email=' + encodeURIComponent(email))
                }, 1500)
              }
            } catch (error) {
              console.error('Auto sign-in error:', error)
              // Fallback to manual sign-in
              setTimeout(() => {
                onSuccess?.()
                router.push('/auth/signin?verified=true&email=' + encodeURIComponent(email))
              }, 1500)
            }
          }, 1500) // Show green tick for 1.5 seconds first
        } else {
          // No auto sign-in token, redirect to manual sign-in
          setTimeout(() => {
            onSuccess?.()
            router.push('/auth/signin?verified=true&email=' + encodeURIComponent(email))
          }, 2000)
        }
      }
    } catch (error) {
      console.error('OTP verification error:', error)
      setError('**Error:** An error occurred. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendOTP = async () => {
    setIsResending(true)
    setError('')

    try {
      const response = await fetch('/api/auth/resend-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      })

      const data = await response.json()

      if (!response.ok) {
        setError(`**Resend failed.** ${data.error || 'Failed to resend verification code.'}`)
      } else {
        setCountdown(60) // 60 second cooldown
        setOtp(['', '', '', '', '', '']) // Clear current OTP
        inputRefs.current[0]?.focus()
      }
    } catch (error) {
      console.error('Resend OTP error:', error)
      setError('**Error:** An error occurred while resending. Please try again.')
    } finally {
      setIsResending(false)
    }
  }

  if (success) {
    return (
      <div className="space-y-6 text-center">
        <div className="space-y-4">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            {signingIn ? (
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
            ) : (
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            )}
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Email Verified!</h2>
            <div className="text-gray-600 mt-2" dangerouslySetInnerHTML={{
              __html: (signingIn
                ? '**Success!** Your email has been successfully verified. Signing you in...'
                : '**Success!** Your email has been successfully verified. You\'ll be redirected to sign in shortly.'
              ).replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            }} />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-900">Verify Your Email</h2>
        <p className="text-gray-600">
          We&apos;ve sent a 6-digit verification code to
        </p>
        <p className="font-semibold text-purple-600">{email}</p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>
            <div dangerouslySetInnerHTML={{
              __html: error.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            }} />
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-4">
        <div className="flex justify-center space-x-3">
          {otp.map((digit, index) => (
            <Input
              key={index}
              ref={(el) => {
                inputRefs.current[index] = el
              }}
              type="text"
              inputMode="numeric"
              maxLength={1}
              value={digit}
              onChange={(e) => handleInputChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={index === 0 ? handlePaste : undefined}
              className="w-12 h-12 text-center text-xl font-bold border-2 focus:border-purple-500"
              disabled={isLoading}
            />
          ))}
        </div>

        <div className="text-center space-y-4">
          <p className="text-sm text-gray-600">
            Didn&apos;t receive the code?
          </p>
          
          <Button
            type="button"
            variant="outline"
            onClick={handleResendOTP}
            disabled={isResending || countdown > 0}
            className="w-full"
          >
            {isResending ? 'Sending...' : countdown > 0 ? `Resend in ${countdown}s` : 'Resend Code'}
          </Button>

          {onBack && (
            <Button
              type="button"
              variant="ghost"
              onClick={onBack}
              className="w-full"
            >
              Back to Sign Up
            </Button>
          )}
        </div>
      </div>

      <div className="text-center text-sm text-gray-500">
        <p>The verification code expires in 15 minutes.</p>
      </div>
    </div>
  )
}
