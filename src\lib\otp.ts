import { prisma } from './prisma'
import nodemailer from 'nodemailer'
import { env } from './config'

// Generate a 6-digit OTP code
export function generateOTP(): string {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

// Create or update verification token in database
export async function createVerificationToken(
  userId: string,
  token: string,
  type: 'email_verification' | 'password_reset' = 'email_verification',
  expiresInMinutes: number = 15
) {
  const expiresAt = new Date(Date.now() + expiresInMinutes * 60 * 1000)

  // Delete any existing tokens of the same type for this user
  await prisma.verificationToken.deleteMany({
    where: {
      userId,
      type,
    },
  })

  // Create new token
  return await prisma.verificationToken.create({
    data: {
      userId,
      token,
      type,
      expiresAt,
    },
  })
}

// Verify OTP token
export async function verifyOTP(
  userId: string,
  token: string,
  type: 'email_verification' | 'password_reset' = 'email_verification'
): Promise<{ valid: boolean; expired?: boolean }> {
  const verificationToken = await prisma.verificationToken.findFirst({
    where: {
      userId,
      token,
      type,
    },
  })

  if (!verificationToken) {
    return { valid: false }
  }

  // Check if token is expired
  if (verificationToken.expiresAt < new Date()) {
    // Clean up expired token
    await prisma.verificationToken.delete({
      where: { id: verificationToken.id },
    })
    return { valid: false, expired: true }
  }

  // Token is valid, clean it up
  await prisma.verificationToken.delete({
    where: { id: verificationToken.id },
  })

  return { valid: true }
}

// Send OTP verification email
export async function sendOTPEmail(
  email: string,
  name: string,
  otp: string,
  type: 'verification' | 'password_reset' = 'verification'
): Promise<void> {
  try {
    console.log(`📧 Attempting to send ${type} email to: ${email}`)

    const transporter = nodemailer.createTransport({
      host: process.env.SUPPORT_EMAIL_HOST,
      port: parseInt(process.env.SUPPORT_EMAIL_PORT || '465'),
      secure: true,
      auth: {
        user: process.env.SUPPORT_EMAIL,
        pass: process.env.SUPPORT_EMAIL_PASSWORD,
      },
    })

  const isVerification = type === 'verification'
  const subject = isVerification 
    ? 'Verify Your Email - DocuChampAI' 
    : 'Reset Your Password - DocuChampAI'
  
  const verificationUrl = `${env.NEXTAUTH_URL}/auth/verify-email?token=${otp}&email=${encodeURIComponent(email)}`

  const mailOptions = {
    from: `DocuChampAI <${process.env.SUPPORT_EMAIL}>`,
    to: email,
    subject,
    // Add email headers for better deliverability and branding
    headers: {
      'X-Mailer': 'DocuChampAI Email Service',
      'X-Priority': '1',
      'X-MSMail-Priority': 'High',
      'List-Unsubscribe': `<mailto:<EMAIL>>`,
      'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
    },
    html: `
      <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #ffffff;">
        <!-- Email Header with Logo -->
        <div style="text-align: center; margin-bottom: 30px; padding: 20px 0; border-bottom: 1px solid #e5e7eb;">
          <img src="${env.NEXTAUTH_URL}/thumbnail_docuchampai.png"
               alt="DocuChampAI"
               style="height: 60px; width: auto; max-width: 200px; display: block; margin: 0 auto;"
               width="200"
               height="60" />
        </div>
        
        <div style="background-color: #f8fafc; padding: 30px; border-radius: 12px; margin: 20px 0;">
          <h2 style="color: #374151; margin-top: 0;">
            ${isVerification ? 'Verify Your Email Address' : 'Reset Your Password'}
          </h2>
          
          <p style="color: #6b7280; font-size: 16px; line-height: 1.6;">
            Hello ${name},
          </p>
          
          <p style="color: #6b7280; font-size: 16px; line-height: 1.6;">
            ${isVerification 
              ? 'Thank you for signing up for DocuChampAI! To complete your registration and start analyzing documents, please verify your email address.'
              : 'We received a request to reset your password for your DocuChampAI account.'
            }
          </p>
          
          <div style="background-color: #ffffff; border: 2px dashed #7c3aed; border-radius: 8px; padding: 20px; margin: 25px 0; text-align: center;">
            <p style="color: #374151; font-size: 14px; margin: 0 0 10px 0; font-weight: 600;">
              Your verification code:
            </p>
            <div style="font-size: 32px; font-weight: bold; color: #7c3aed; letter-spacing: 8px; font-family: 'Courier New', monospace;">
              ${otp}
            </div>
            <p style="color: #6b7280; font-size: 12px; margin: 10px 0 0 0;">
              This code expires in 15 minutes
            </p>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" 
               style="background-color: #7c3aed; color: white; padding: 14px 28px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block;">
              ${isVerification ? 'Verify Email Address' : 'Reset Password'}
            </a>
          </div>
          
          <p style="color: #6b7280; font-size: 14px; line-height: 1.6;">
            If the button doesn't work, you can also copy and paste this link into your browser:
          </p>
          <p style="color: #7c3aed; font-size: 14px; word-break: break-all;">
            ${verificationUrl}
          </p>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px;">
          <p style="margin: 0;">
            If you didn't ${isVerification ? 'create an account' : 'request a password reset'} with DocuChampAI, you can safely ignore this email.
          </p>
          <p style="margin: 10px 0 0 0;">
            Need help? Contact us at <a href="mailto:<EMAIL>" style="color: #7c3aed;"><EMAIL></a>
          </p>
        </div>
      </div>
    `,
  }

    // Send the email and capture the result
    const result = await transporter.sendMail(mailOptions)

    console.log(`✅ Email sent successfully to ${email}:`, {
      messageId: result.messageId,
      accepted: result.accepted,
      rejected: result.rejected,
      response: result.response
    })

  } catch (error) {
    console.error(`❌ Failed to send ${type} email to ${email}:`, error)

    // Log specific error details for debugging
    if (error instanceof Error) {
      console.error('Email error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      })
    }

    // Re-throw the error so calling functions can handle it appropriately
    throw new Error(`Failed to send ${type} email: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Clean up expired tokens (can be called periodically)
export async function cleanupExpiredTokens(): Promise<number> {
  const result = await prisma.verificationToken.deleteMany({
    where: {
      expiresAt: {
        lt: new Date(),
      },
    },
  })
  
  return result.count
}
