import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { getUserById, deductCreditsFromUser, addCreditsToUser, recordUsage } from '@/lib/db'
import { getDocumentAnalysisCreditCost } from '@/lib/config'
import { env } from '@/lib/config'
import { v4 as uuidv4 } from 'uuid'
import { validateFile } from '@/lib/file-utils'
import { getDefaultSectionNames } from '@/lib/analysis-config'
import { getAnonymousUserId } from '@/lib/trial-usage-tracker'

// POST /api/analyze
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const userId = (session?.user as any)?.id

    // Parse form data first to check if it's a trial request
    const formData = await request.formData()
    const isTrial = formData.get('isTrial') === 'true'

    // For trial requests, we don't require authentication
    if (!userId && !isTrial) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // For registered users, get user and check credits
    let user = null
    let creditCost = 0

    if (!isTrial && userId) {
      user = await getUserById(userId)
      if (!user) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 })
      }

      creditCost = getDocumentAnalysisCreditCost()
      if (user.credits < creditCost) {
        return NextResponse.json({
          error: 'Insufficient credits',
          required: creditCost,
          available: user.credits
        }, { status: 402 })
      }
    }

    // Extract data from already parsed form data
    const file = formData.get('file') as File
    const analysisType = formData.get('analysisType') as string
    const sections = formData.get('sections') as string
    const language = formData.get('language') as string || 'English'
    const reportLength = formData.get('reportLength') as string || 'long'

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    if (!analysisType) {
      return NextResponse.json({ error: 'Analysis type is required' }, { status: 400 })
    }

    // Use centralized file validation with trial-specific limits
    const validation = validateFile(file)
    if (!validation.isValid) {
      return NextResponse.json({ error: validation.error }, { status: 400 })
    }

    // Additional validation for trial users (1MB limit)
    if (isTrial && file.size > 1 * 1024 * 1024) {
      return NextResponse.json({
        error: 'File too large for trial. Maximum size is 1MB. Please sign up for larger file support.'
      }, { status: 400 })
    }

    // Generate operation ID - prefix with 'trial_' for trial users
    const operationId = isTrial ? `trial_${uuidv4()}` : uuidv4()

    // For registered users, deduct credits and record usage
    if (!isTrial && userId) {
      // Deduct credits immediately
      await deductCreditsFromUser(userId, creditCost)

      // Record usage
      await recordUsage({
        userId,
        operationId,
        operationType: 'document_analysis',
        creditSpent: creditCost,
        status: 'pending',
        fileName: file.name,
        fileSize: file.size,
        metadata: {
          analysisType: analysisType || 'office',
          sections: sections ? JSON.parse(sections) : getDefaultSectionNames(analysisType || 'office'),
          language,
          reportLength,
          fileType: file.type,
          originalFileName: file.name,
          isTrial: false
        }
      })
    }

    // For trial users, generate a proper anonymous user ID
    const effectiveUserId = isTrial ? getAnonymousUserId() : userId!
    const effectiveAnalysisType = analysisType || 'office' // Default to office for trials

    // Prepare AZF_DOCUCHAMP form data according to API specification
    const azureFormData = new FormData()
    azureFormData.append('file', file)
    azureFormData.append('userId', effectiveUserId)
    azureFormData.append('operationId', operationId)
    azureFormData.append('analysis_type', effectiveAnalysisType)

    // Use default sections for the analysis type if no sections provided
    let sectionsArray: string[] = []
    if (sections) {
      const parsedSections = JSON.parse(sections)
      // Use parsed sections if they're not empty, otherwise use defaults
      sectionsArray = parsedSections.length > 0 ? parsedSections : getDefaultSectionNames(effectiveAnalysisType)
    } else {
      // Use default sections for the analysis type
      sectionsArray = getDefaultSectionNames(effectiveAnalysisType)
    }
    azureFormData.append('sections', sectionsArray.join(','))
    azureFormData.append('language', language)
    azureFormData.append('report_length', reportLength)
    azureFormData.append('include_docx', 'true')
    azureFormData.append('analyze_complete_webhook_url', `${env.NEXTAUTH_URL}/api/webhooks/analysis-complete`)
    // Add trial flag for AZF to handle differently if needed
    if (isTrial) {
      azureFormData.append('is_trial', 'true')
    }

    // Log operation start for monitoring (production-safe)
    console.log(`📤 Starting analysis operation ${operationId} for user ${effectiveUserId}`)

    try {
      // Call AZF_DOCUCHAMP Azure Durable Function with function key
      const azfEndpoint = `${env.AZF_DOCUCHAMP_FUNCTION_ENDPOINT}?code=${env.AZF_DOCUCHAMP_FUNCTION_KEY}`
      const azureResponse = await fetch(azfEndpoint, {
        method: 'POST',
        body: azureFormData
      })

      if (!azureResponse.ok) {
        const errorText = await azureResponse.text()
        throw new Error(`AZF_DOCUCHAMP error: ${azureResponse.status} - ${errorText}`)
      }

      const azureResult = await azureResponse.json()

      // Update usage record with AZF_DOCUCHAMP operation details (for registered users only)
      if (!isTrial && userId) {
        const { updateUsageRecord } = await import('@/lib/db')
        await updateUsageRecord(operationId, {
          status: 'processing',
          metadata: {
            analysisType,
            sections: sections ? JSON.parse(sections) : [],
            language,
            reportLength,
            fileType: file.type,
            originalFileName: file.name,
            azfInstanceId: azureResult.id,
            statusQueryGetUri: azureResult.statusQueryGetUri,
            startedAt: new Date().toISOString()
          }
        })
      }

      return NextResponse.json({
        success: true,
        operationId,
        message: 'Document analysis started successfully! You will be notified when it completes.',
        estimatedTime: '3-5 minutes',
        status: 'processing',
        azfInstanceId: azureResult.id
      })

    } catch (error: any) {
      console.error('Error calling AZF_DOCUCHAMP:', error)
      
      // For registered users, refund credits and update usage record
      if (!isTrial && userId) {
        // Refund credits on error
        await addCreditsToUser(userId, creditCost)

        // Update usage record to failed
        const { updateUsageRecord } = await import('@/lib/db')
        await updateUsageRecord(operationId, {
          status: 'failed',
          metadata: {
            error: error.message,
            failedAt: new Date().toISOString()
          }
        })
      }

      return NextResponse.json({
        error: 'Failed to start document analysis',
        details: error.message
      }, { status: 500 })
    }

  } catch (error: any) {
    console.error('Error in document analysis:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 })
  }
}
