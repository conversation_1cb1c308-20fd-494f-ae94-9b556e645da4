'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { AuthModal } from '@/components/auth/auth-modal'
import { CurrencySwitch } from '@/components/ui/currency-switch'
import { getCreditPackages } from '@/lib/config'
import { formatCurrency, convertCurrency } from '@/lib/utils'
import { Check, Star } from 'lucide-react'
import { FAQAccordion } from '@/components/ui/faq-accordion'
import { useTranslation } from '@/hooks/use-translation'

export default function PricingPage() {
  const { data: session } = useSession()
  const [loading, setLoading] = useState<string | null>(null)
  const [currency, setCurrency] = useState<'HKD' | 'USD'>('HKD')
  const [authModal, setAuthModal] = useState({
    isOpen: false,
    mode: 'signin' as 'signin' | 'signup',
    pendingAction: null as { type: string; packageIndex: number } | null
  })
  const { t, isReady } = useTranslation('pricing')

  // Helper function to safely get array from translation
  const getTranslationArray = (key: string, fallback: any[] = []): any[] => {
    if (!isReady) return fallback
    const value = t(key)
    return Array.isArray(value) ? value : fallback
  }

  // FAQ data from translations - only use when ready
  const faqItems = getTranslationArray('faq.items', []) as Array<{ question: string; answer: string }>



  const creditPackages = getCreditPackages()

  const handleBuyCredits = async (packageIndex: number) => {
    // Check if this is the Enterprise plan
    const pkg = creditPackages[packageIndex]
    if (pkg.name === 'Enterprise') {
      // Redirect to contact page with prefilled form
      const params = new URLSearchParams({
        subject: 'Enterprise Plan Enquiry',
        message: 'I am interested in the Enterprise plan. Please provide me with more details about pricing, features, and implementation.'
      })
      window.location.href = `/contact?${params.toString()}`
      return
    }

    if (!session) {
      // Show auth modal with pending action
      setAuthModal({
        isOpen: true,
        mode: 'signin',
        pendingAction: {
          type: 'buyCredits',
          packageIndex: packageIndex
        }
      })
      return
    }

    setLoading(`credits-${packageIndex}`)
    try {
      const response = await fetch('/api/credits/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ packageId: packageIndex.toString() }),
      })

      if (response.ok) {
        const { url, type } = await response.json()
        if (type === 'portal') {
          // User has active subscription, redirect to customer portal
          window.location.href = url
        } else {
          // New subscription, redirect to payment portal
          window.location.href = url
        }
      } else {
        console.error('Failed to create checkout session')
      }
    } catch (error) {
      console.error('Error creating checkout session:', error)
    } finally {
      setLoading(null)
    }
  }

  // Handle pending action after login
  useEffect(() => {
    if (session && authModal.pendingAction) {
      // Execute the pending action after successful login
      const action = authModal.pendingAction
      setAuthModal(prev => ({ ...prev, pendingAction: null, isOpen: false }))

      if (action.type === 'buyCredits') {
        // Trigger the credit purchase
        handleBuyCredits(action.packageIndex)
      }
    }
  }, [session])

  // Show loading state until translations are ready
  if (!isReady) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    )
  }

  return (
    <>
      {/* Pricing Plans Section */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4 relative">
            <h1 className="text-4xl font-bold text-gray-900">
              {t('title')}
            </h1>
            {/* Currency Switch positioned to the right */}
            <div className="absolute right-0 hidden sm:block">
              <CurrencySwitch
                currentCurrency={currency}
                onCurrencyChange={setCurrency}
              />
            </div>
          </div>

          {/* Mobile Currency Switch - centered below title */}
          <div className="flex justify-center mb-4 sm:hidden">
            <CurrencySwitch
              currentCurrency={currency}
              onCurrencyChange={setCurrency}
            />
          </div>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('subtitle')}
          </p>
        </div>

        {/* Credit Packages */}
        <div className="flex justify-center">
          <div id="credits" className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-8 mb-12 max-w-7xl">
            {creditPackages.map((pkg, index) => (
              <div key={index} className="relative w-full min-w-[280px]">
                <div
                  className={`rounded-lg text-center relative h-full flex flex-col w-full pricing-card group ${
                    pkg.name === 'Enterprise'
                      ? 'bg-gray-900 text-white p-8 shadow-2xl hover:shadow-gray-800/50 enterprise-glow'
                      : pkg.name === 'Professional'
                        ? 'bg-gradient-to-r from-purple-500 via-pink-500 to-purple-600 p-[3px] animate-gradient-x shadow-2xl hover:shadow-purple-500/30'
                        : pkg.name === 'Premium'
                          ? 'bg-white p-8 shadow-lg hover:shadow-blue-200/50 hover:shadow-xl border border-blue-200'
                          : 'bg-white p-8 shadow-lg hover:shadow-gray-300/50 hover:shadow-xl border border-gray-200'
                  }`}
                  style={pkg.name === 'Professional' ? {
                    backgroundSize: '200% 200%',
                    animation: 'gradient-x 3s ease infinite',
                    filter: 'drop-shadow(0 10px 25px rgba(168, 85, 247, 0.15))'
                  } : {}}
                >
                  {/* Most Popular Badge - integrated into the card border */}
                  {pkg.name === 'Professional' && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                      <span className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1.5 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-bold flex items-center shadow-lg whitespace-nowrap">
                        <Star className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2 flex-shrink-0" />
                        {t('mostPopular')}
                      </span>
                    </div>
                  )}
                  {/* Inner content container for Professional package with gradient border */}
                  <div className={pkg.name === 'Professional' ? 'bg-white rounded-lg p-8 h-full flex flex-col relative overflow-hidden' : 'h-full flex flex-col relative overflow-hidden'}>

                  {/* Shimmer effect overlay for non-Professional cards */}
                  {pkg.name !== 'Professional' && pkg.name !== 'Enterprise' && (
                    <div className="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"
                           style={{backgroundSize: '200% 100%'}} />
                    </div>
                  )}

                <h3 className={`text-2xl font-bold mb-2 ${
                  pkg.name === 'Enterprise' ? 'text-white' : 'text-gray-900'
                }`}>{pkg.name}</h3>
                <p className={`text-sm mb-4 ${
                  pkg.name === 'Enterprise' ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  {pkg.name === 'Enterprise' ? t('enterpriseDescription') : pkg.description}
                </p>
                <div className="mb-4">
                  {pkg.name === 'Enterprise' ? (
                    <div>
                      <span className="text-4xl font-bold text-white">
                        {t('custom')}
                      </span>
                    </div>
                  ) : (
                    <>
                      <span className="text-4xl font-bold text-gray-900">
                        {formatCurrency(convertCurrency(pkg.price, 'HKD', currency), currency)}
                      </span>
                      <span className="text-gray-500">{t('perMonth')}</span>
                    </>
                  )}
                </div>
                <p className={`mb-4 ${
                  pkg.name === 'Enterprise' ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  {pkg.name === 'Enterprise' ? t('customCreditLimit') : t('creditsPerMonth', { count: pkg.credits })}
                </p>
                {pkg.name !== 'Enterprise' && (
                  <p className="text-sm text-gray-500 mb-6">
                    {formatCurrency(convertCurrency(pkg.price, 'HKD', currency) / pkg.credits, currency)} {t('perCredit')}
                  </p>
                )}

                <div className="mb-6 text-left flex-grow">
                  <h4 className={`font-semibold mb-2 ${
                    pkg.name === 'Enterprise' ? 'text-white' : 'text-gray-900'
                  }`}>{t('whatYouGet')}</h4>
                  <ul className={`space-y-2 text-sm ${
                    pkg.name === 'Enterprise' ? 'text-gray-300' : 'text-gray-600'
                  }`}>
                    {pkg.name === 'Enterprise' ? (
                      <>
                        <li className="flex items-center">
                          <Check className="w-4 h-4 text-green-400 mr-2" />
                          {t('tailorMadeCreditLimit')}
                        </li>
                        <li className="flex items-center">
                          <Check className="w-4 h-4 text-green-400 mr-2" />
                          {t('enterpriseGradeSecurity')}
                        </li>
                        <li className="flex items-center">
                          <Check className="w-4 h-4 text-green-400 mr-2" />
                          {t('customizedTemplates')}
                        </li>
                        <li className="flex items-center">
                          <Check className="w-4 h-4 text-green-400 mr-2" />
                          {t('dedicatedSupport')}
                        </li>
                        <li className="flex items-center">
                          <Check className="w-4 h-4 text-green-400 mr-2" />
                          {t('accessToMostUpdatedLLM')}
                        </li>
                      </>
                    ) : (
                      <>
                        <li className="flex items-center">
                          <Check className="w-4 h-4 text-green-500 mr-2" />
                          {t('creditsPerMonth', { count: pkg.credits })}
                        </li>
                        <li className="flex items-center">
                          <Check className="w-4 h-4 text-green-500 mr-2" />
                          {t('creditsRenewAutomatically')}
                        </li>
                        <li className="flex items-center">
                          <Check className="w-4 h-4 text-green-500 mr-2" />
                          {t('accessToAllFeatures')}
                        </li>
                        <li className="flex items-center">
                          <Check className="w-4 h-4 text-green-500 mr-2" />
                          {t('cancelAnytime')}
                        </li>
                      </>
                    )}
                  </ul>
                </div>

                <div className="mt-auto">
                  <Button
                    onClick={() => handleBuyCredits(index)}
                    disabled={loading === `credits-${index}`}
                    className={`w-full text-white transition-all duration-300 transform hover:scale-105 !rounded-md force-rounded ${
                      pkg.name === 'Enterprise'
                        ? 'bg-white text-gray-900 hover:bg-gray-100 border-2 border-white hover:shadow-lg !rounded-md hover:!rounded-md'
                        : pkg.name === 'Professional'
                          ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 shadow-lg hover:shadow-purple-500/50 !rounded-md hover:!rounded-md'
                          : pkg.name === 'Premium'
                            ? 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg hover:shadow-blue-500/50 !rounded-md hover:!rounded-md'
                            : 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 shadow-lg hover:shadow-gray-500/50 !rounded-md hover:!rounded-md'
                    }`}
                  >
                    {loading === `credits-${index}` ? t('loading') :
                     pkg.name === 'Enterprise' ? t('contactSales') : t('subscribeNow')}
                  </Button>
                </div>
                </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        </div>
      </section>



      {/* FAQ Section - Full Width */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">{t('faq.title')}</h2>

          <div className="max-w-4xl mx-auto">
            <FAQAccordion items={faqItems} />
          </div>

          {/* Contact Section */}
          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4">
              {t('stillHaveQuestions')}
            </p>
            <Link href="/contact" className="text-purple-500 hover:text-purple-600 font-medium">
              {t('contactOurTeam')}
            </Link>
          </div>
        </div>
      </section>

      {/* Auth Modal */}
      <AuthModal
        isOpen={authModal.isOpen}
        onClose={() => setAuthModal(prev => ({ ...prev, isOpen: false }))}
        initialMode={authModal.mode}
      />
    </>
  )
}
