'use client'

import { useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Loader2 } from 'lucide-react'

export default function AuthCallbackPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'loading') return

    if (status === 'authenticated' && session) {
      // Check if this is a new SSO user
      const isNewUser = (session as any).isNewUser

      if (isNewUser) {
        // Mark as new user and clear any existing tutorial state for fresh start
        sessionStorage.setItem('isNewUser', 'true')
        localStorage.removeItem('docuchampai_tutorial_state')
        console.log('✅ New SSO user detected - will show welcome message and tutorial')
        
        // Redirect to document analysis with newUser flag
        router.push('/features/document-analysis?newUser=true')
      } else {
        // Existing user, redirect normally
        router.push('/features/document-analysis')
      }
    } else if (status === 'unauthenticated') {
      // Authentication failed, redirect to sign-in
      router.push('/auth/signin?error=Authentication failed')
    }
  }, [session, status, router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-purple-600" />
        <h2 className="text-lg font-semibold text-gray-900 mb-2">
          Completing sign-in...
        </h2>
        <p className="text-gray-600">
          Please wait while we set up your account.
        </p>
      </div>
    </div>
  )
}
