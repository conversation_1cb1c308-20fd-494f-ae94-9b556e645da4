import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

const MAX_TEMPLATES_PER_USE_CASE = 5

// GET /api/templates?useCaseId=garment_bom
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const userId = (session?.user as any)?.id
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const useCaseId = searchParams.get('useCaseId')

    if (!useCaseId) {
      return NextResponse.json({ error: 'useCaseId is required' }, { status: 400 })
    }

    const templates = await prisma.documentAnalysisTemplate.findMany({
      where: {
        userId,
        useCaseId
      },
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        name: true,
        sections: true,
        useCaseId: true,
        isPublic: true,
        usageCount: true,
        createdAt: true
      }
    })

    // Transform the data to match the expected format
    const formattedTemplates = templates.map(template => ({
      id: template.id,
      name: template.name,
      sections: template.sections as string[],
      useCaseId: template.useCaseId,
      isPublic: template.isPublic,
      usageCount: template.usageCount,
      createdAt: template.createdAt.toISOString()
    }))

    return NextResponse.json(formattedTemplates)
  } catch (error) {
    console.error('Error fetching templates:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/templates
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const userId = (session?.user as any)?.id
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { useCaseId, name, sections } = await request.json()

    if (!useCaseId || !name || !sections || !Array.isArray(sections)) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Check if user has reached the limit for this use case
    const existingCount = await prisma.documentAnalysisTemplate.count({
      where: {
        userId,
        useCaseId
      }
    })

    if (existingCount >= MAX_TEMPLATES_PER_USE_CASE) {
      return NextResponse.json({ 
        error: `Maximum ${MAX_TEMPLATES_PER_USE_CASE} templates allowed per use case` 
      }, { status: 400 })
    }

    // Check if template name already exists for this user and use case
    const existingTemplate = await prisma.documentAnalysisTemplate.findFirst({
      where: {
        userId,
        useCaseId,
        name
      }
    })

    if (existingTemplate) {
      return NextResponse.json({ error: 'Template name already exists' }, { status: 400 })
    }

    // Create the template
    const template = await prisma.documentAnalysisTemplate.create({
      data: {
        userId,
        useCaseId,
        name,
        sections: sections as any, // Prisma handles JSON serialization
        isPublic: false,
        usageCount: 0
      },
      select: {
        id: true,
        name: true,
        sections: true,
        useCaseId: true,
        isPublic: true,
        usageCount: true,
        createdAt: true
      }
    })

    const formattedTemplate = {
      id: template.id,
      name: template.name,
      sections: template.sections as string[],
      useCaseId: template.useCaseId,
      isPublic: template.isPublic,
      usageCount: template.usageCount,
      createdAt: template.createdAt.toISOString()
    }

    return NextResponse.json(formattedTemplate, { status: 201 })
  } catch (error) {
    console.error('Error creating template:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
