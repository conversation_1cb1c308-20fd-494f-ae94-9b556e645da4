'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ArrowLeft, Download, FileText, XCircle, Search, Filter, ChevronLeft, ChevronRight } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { showNotification } from '@/lib/notifications'

interface AnalysisRecord {
  operationId: string
  operationType: string
  status: string
  creditSpent: number
  fileName: string
  fileSize: number
  fileLink: string | null
  createdAt: string
  updatedAt: string
  completedAt: string | null
  metadata: any
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export default function AnalysisHistoryPage() {
  const { data: session } = useSession()
  const [records, setRecords] = useState<AnalysisRecord[]>([])
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  // Filter states
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [dateFrom, setDateFrom] = useState('')
  const [dateTo, setDateTo] = useState('')
  const [showFilters, setShowFilters] = useState(false)

  const fetchHistory = useCallback(async () => {
    try {
      setLoading(true)
      setError('')

      // Build query parameters
      const params = new URLSearchParams({
        page: '1', // Always use page 1 for initial load and filter changes
        limit: '20',
      })

      if (statusFilter && statusFilter !== 'all') {
        params.append('status', statusFilter)
      }

      if (searchTerm.trim()) {
        params.append('search', searchTerm.trim())
      }

      if (dateFrom) {
        params.append('dateFrom', dateFrom)
      }

      if (dateTo) {
        params.append('dateTo', dateTo)
      }

      const response = await fetch(`/api/analyze/history?${params.toString()}`)

      if (!response.ok) {
        throw new Error('Failed to fetch history')
      }

      const data = await response.json()
      setRecords(data.records || [])
      setPagination(data.pagination || {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      })
    } catch (error: any) {
      console.error('Error fetching history:', error)
      // Extract error code for user display, keep full error in console
      const userFriendlyError = error.message?.match(/\b\d{3}\b/)
        ? `Error ${error.message.match(/\b\d{3}\b/)[0]}`
        : 'Failed to load analysis history'
      setError(userFriendlyError)
    } finally {
      setLoading(false)
    }
  }, [statusFilter, dateFrom, dateTo])

  useEffect(() => {
    if (session?.user) {
      fetchHistory()
    }
  }, [session, fetchHistory])

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }))
    // Trigger a re-fetch with the current search term
    fetchHistoryWithSearch()
  }

  const fetchHistoryWithSearch = async () => {
    try {
      setLoading(true)
      setError('')

      // Build query parameters
      const params = new URLSearchParams({
        page: '1', // Always start from page 1 when searching
        limit: pagination.limit.toString(),
      })

      if (statusFilter && statusFilter !== 'all') {
        params.append('status', statusFilter)
      }

      if (searchTerm.trim()) {
        params.append('search', searchTerm.trim())
      }

      if (dateFrom) {
        params.append('dateFrom', dateFrom)
      }

      if (dateTo) {
        params.append('dateTo', dateTo)
      }

      const response = await fetch(`/api/analyze/history?${params.toString()}`)

      if (!response.ok) {
        throw new Error('Failed to fetch history')
      }

      const data = await response.json()
      setRecords(data.records || [])
      setPagination(data.pagination || { ...pagination, page: 1 })
    } catch (error: any) {
      console.error('Error fetching history:', error)
      // Extract error code for user display, keep full error in console
      const userFriendlyError = error.message?.match(/\b\d{3}\b/)
        ? `Error ${error.message.match(/\b\d{3}\b/)[0]}`
        : 'Failed to load analysis history'
      setError(userFriendlyError)
    } finally {
      setLoading(false)
    }
  }

  const handleClearFilters = async () => {
    // Clear all filter states
    setSearchTerm('')
    setStatusFilter('all')
    setDateFrom('')
    setDateTo('')
    setPagination(prev => ({ ...prev, page: 1 }))

    // Fetch history without any filters by calling the API directly
    try {
      setLoading(true)
      setError('')

      const response = await fetch('/api/analyze/history?page=1&limit=20')

      if (!response.ok) {
        throw new Error('Failed to fetch history')
      }

      const data = await response.json()
      setRecords(data.records || [])
      setPagination(data.pagination || pagination)
    } catch (error: any) {
      console.error('Error fetching history:', error)
      // Extract error code for user display, keep full error in console
      const userFriendlyError = error.message?.match(/\b\d{3}\b/)
        ? `Error ${error.message.match(/\b\d{3}\b/)[0]}`
        : 'Failed to load analysis history'
      setError(userFriendlyError)
    } finally {
      setLoading(false)
    }
  }

  const handlePageChange = async (newPage: number) => {
    try {
      setLoading(true)
      setError('')

      const params = new URLSearchParams({
        page: newPage.toString(),
        limit: '20',
      })

      if (statusFilter && statusFilter !== 'all') {
        params.append('status', statusFilter)
      }

      if (searchTerm.trim()) {
        params.append('search', searchTerm.trim())
      }

      if (dateFrom) {
        params.append('dateFrom', dateFrom)
      }

      if (dateTo) {
        params.append('dateTo', dateTo)
      }

      const response = await fetch(`/api/analyze/history?${params.toString()}`)

      if (!response.ok) {
        throw new Error('Failed to fetch history')
      }

      const data = await response.json()
      setRecords(data.records || [])
      setPagination(data.pagination || {
        page: newPage,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      })
    } catch (error: any) {
      console.error('Error fetching history:', error)
      // Extract error code for user display, keep full error in console
      const userFriendlyError = error.message?.match(/\b\d{3}\b/)
        ? `Error ${error.message.match(/\b\d{3}\b/)[0]}`
        : 'Failed to load analysis history'
      setError(userFriendlyError)
    } finally {
      setLoading(false)
    }
  }

  // Check if a processing record is stale (older than 6 minutes)
  const isStaleProcessing = (record: AnalysisRecord) => {
    if (record.status !== 'processing') return false
    const now = new Date()
    const createdAt = new Date(record.createdAt)
    const minutesElapsed = (now.getTime() - createdAt.getTime()) / (1000 * 60)
    return minutesElapsed > 6 // Consider stale if processing for more than 6 minutes
  }

  // Get effective status (convert stale processing to failed)
  const getEffectiveStatus = (record: AnalysisRecord) => {
    if (isStaleProcessing(record)) {
      return 'failed'
    }
    return record.status
  }

  // Removed unused getStatusIcon function

  const getStatusText = (record: AnalysisRecord) => {
    const status = getEffectiveStatus(record)
    switch (status) {
      case 'completed':
        return 'Completed'
      case 'failed':
        return isStaleProcessing(record) ? 'Timed Out' : 'Failed'
      case 'processing':
        return 'Processing'
      case 'pending':
        return 'Pending'
      default:
        return status
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleDownload = async (fileLink: string, fileName: string) => {
    try {
      // For Azure Blob Storage SAS URLs, we can directly open them in a new tab
      // This avoids CORS issues with fetch
      if (fileLink.includes('blob.core.windows.net') || fileLink.includes('sas')) {
        // Create a temporary link and click it to trigger download
        const a = document.createElement('a')
        a.href = fileLink
        a.download = fileName.replace(/\.[^/.]+$/, '_analyzed.docx')
        a.target = '_blank'
        a.rel = 'noopener noreferrer'
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      } else {
        // Fallback for other URLs - try fetch approach
        const response = await fetch(fileLink, {
          mode: 'cors',
          credentials: 'omit'
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.download = fileName.replace(/\.[^/.]+$/, '_analyzed.docx')
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Download failed:', error)
      showNotification(
        'Download Failed',
        'Failed to download file. The file may have expired or is not accessible.',
        { requireInteraction: false }
      )
    }
  }

  // No need to filter by 30 days anymore since we have date filters
  const displayRecords = records

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading analysis history...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8 flex items-center gap-4">
          <Link href="/features/document-analysis">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Analysis
            </Button>
          </Link>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <XCircle className="w-5 h-5 text-red-400 mr-3" />
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        {/* Filters Section */}
        <div className="bg-white rounded-lg shadow-sm border mb-6">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Search & Filter</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="w-4 h-4 mr-2" />
                {showFilters ? 'Hide Filters' : 'Show Filters'}
              </Button>
            </div>
          </div>

          <div className="p-4">
            {/* Search Bar */}
            <div className="flex gap-3 mb-4">
              <div className="flex-1">
                <Input
                  placeholder="Search by file name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <Button onClick={handleSearch} className="bg-purple-600 hover:bg-purple-700 text-white">
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
            </div>

            {/* Advanced Filters */}
            {showFilters && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent className="bg-white border border-gray-200 shadow-lg">
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="processing">Processing</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">From Date</label>
                  <Input
                    type="date"
                    value={dateFrom}
                    onChange={(e) => setDateFrom(e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">To Date</label>
                  <Input
                    type="date"
                    value={dateTo}
                    onChange={(e) => setDateTo(e.target.value)}
                  />
                </div>
              </div>
            )}

            {/* Reset Button */}
            {(searchTerm || statusFilter !== 'all' || dateFrom || dateTo) && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <Button variant="outline" size="sm" onClick={handleClearFilters}>
                  Reset All
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Records List */}
        {displayRecords.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm border p-12 text-center">
            <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm || statusFilter !== 'all' || dateFrom || dateTo
                ? 'No matching records found'
                : 'No analysis history'}
            </h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || statusFilter !== 'all' || dateFrom || dateTo
                ? 'Try adjusting your search criteria or filters.'
                : 'You haven\'t performed any document analysis yet.'}
            </p>
            {!(searchTerm || statusFilter !== 'all' || dateFrom || dateTo) && (
              <Link href="/features/document-analysis">
                <Button className="bg-purple-600 hover:bg-purple-700">
                  Start Your First Analysis
                </Button>
              </Link>
            )}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-medium text-gray-900">
                  Analysis History
                </h2>
                <div className="text-sm text-gray-500">
                  Showing {((pagination.page - 1) * pagination.limit) + 1}-{Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} records
                </div>
              </div>
            </div>

            <div className="divide-y divide-gray-200">
              {displayRecords.map((record) => (
                <div key={record.operationId} className="p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-medium text-gray-900">
                          {record.fileName}
                        </h3>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          getEffectiveStatus(record) === 'completed' ? 'bg-green-100 text-green-800' :
                          getEffectiveStatus(record) === 'failed' ? 'bg-red-100 text-red-800' :
                          getEffectiveStatus(record) === 'processing' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {getStatusText(record)}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                        <div>
                          <span className="font-medium">File Size:</span> {formatFileSize(record.fileSize)}
                        </div>
                        <div>
                          <span className="font-medium">Credits Used:</span> {record.creditSpent}
                        </div>
                        {record.completedAt && (
                          <div>
                            <span className="font-medium">Completed:</span> {formatDistanceToNow(new Date(record.completedAt), { addSuffix: true })}
                          </div>
                        )}
                      </div>

                      {record.metadata?.error && (
                        <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                          <p className="text-sm text-red-700">
                            <span className="font-medium">Error:</span> {record.metadata.error}
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="ml-6 flex-shrink-0">
                      {getEffectiveStatus(record) === 'completed' && record.fileLink ? (
                        <Button
                          onClick={() => handleDownload(record.fileLink!, record.fileName)}
                          className="bg-purple-600 hover:bg-purple-700 text-white"
                        >
                          <Download className="w-4 h-4 mr-2" />
                          Download
                        </Button>
                      ) : (
                        <Button disabled variant="outline">
                          {getEffectiveStatus(record) === 'processing' ? 'Processing...' :
                           isStaleProcessing(record) ? 'Timed Out' : 'Not Available'}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    Page {pagination.page} of {pagination.totalPages}
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={!pagination.hasPrev}
                    >
                      <ChevronLeft className="w-4 h-4 mr-1" />
                      Previous
                    </Button>

                    {/* Page numbers */}
                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        let pageNum
                        if (pagination.totalPages <= 5) {
                          pageNum = i + 1
                        } else if (pagination.page <= 3) {
                          pageNum = i + 1
                        } else if (pagination.page >= pagination.totalPages - 2) {
                          pageNum = pagination.totalPages - 4 + i
                        } else {
                          pageNum = pagination.page - 2 + i
                        }

                        return (
                          <Button
                            key={pageNum}
                            variant={pageNum === pagination.page ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(pageNum)}
                            className={pageNum === pagination.page ? "bg-purple-600 hover:bg-purple-700" : ""}
                          >
                            {pageNum}
                          </Button>
                        )
                      })}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={!pagination.hasNext}
                    >
                      Next
                      <ChevronRight className="w-4 h-4 ml-1" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Footer Note */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>Analysis history is kept for your records. Completed files are available for download.</p>
        </div>
      </div>
    </div>
  )
}
