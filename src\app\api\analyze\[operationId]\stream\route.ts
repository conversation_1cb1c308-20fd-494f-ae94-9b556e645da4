import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { getUsageRecordByOperationId } from '@/lib/db'

// Store SSE connections for real-time notifications
// Map: operationId -> ReadableStreamDefaultController
export const sseConnections = new Map<string, ReadableStreamDefaultController<any>>()

// GET /api/analyze/[operationId]/stream - Server-Sent Events endpoint
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ operationId: string }> }
) {
  try {
    const { operationId } = await params

    // Check if this is a trial operation (trial operations start with "trial_")
    const isTrial = operationId.startsWith('trial_')
    let usageRecord = null

    if (!isTrial) {
      // For non-trial users, verify authentication
      const session = await getServerSession(authOptions)
      const userId = (session?.user as any)?.id

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Verify the operation belongs to the current user
      usageRecord = await getUsageRecordByOperationId(operationId)
      if (!usageRecord) {
        return NextResponse.json({ error: 'Operation not found' }, { status: 404 })
      }

      if (usageRecord.userId !== userId) {
        return NextResponse.json({ error: 'Access denied' }, { status: 403 })
      }

      // If operation is already completed, return the result immediately
      if (usageRecord.status === 'completed' || usageRecord.status === 'failed') {
        return NextResponse.json({
          operationId: usageRecord.operationId,
          success: usageRecord.status === 'completed',
          markdownContent: (usageRecord.metadata as any)?.markdownContent || null,
          output_file_blob_sas_url: usageRecord.fileLink,
          metadata: usageRecord.metadata
        })
      }
    }

    // For trial operations, we skip authentication and database checks

    // Create SSE stream for real-time updates
    const stream = new ReadableStream({
      start(controller) {
        // Store the connection for this operationId
        sseConnections.set(operationId, controller)

        // Send initial connection confirmation
        controller.enqueue(`data: ${JSON.stringify({
          type: 'connected',
          operationId,
          message: 'Connected to analysis stream'
        })}\n\n`)

        // Send keep-alive ping every 30 seconds
        const keepAliveInterval = setInterval(() => {
          try {
            controller.enqueue(`data: ${JSON.stringify({
              type: 'ping',
              timestamp: new Date().toISOString()
            })}\n\n`)
          } catch (error) {
            // Connection closed, clean up
            clearInterval(keepAliveInterval)
            sseConnections.delete(operationId)
          }
        }, 30000)

        // Set timeout to 2 minutes (120 seconds) for all operations
        const timeoutSeconds = 120 // 2 minutes
        const timeoutDuration = timeoutSeconds * 1000

        const analysisTimeout = setTimeout(() => {
          console.warn(`⏰ Analysis timeout for operation ${operationId} after ${timeoutSeconds} seconds`)
          try {
            controller.enqueue(`data: ${JSON.stringify({
              type: 'analysis_timeout',
              operationId,
              message: 'Analysis is taking longer than expected. Please try again or contact support.',
              timestamp: new Date().toISOString()
            })}\n\n`)

            // Clean up and close connection
            clearInterval(keepAliveInterval)
            sseConnections.delete(operationId)
            setTimeout(() => {
              try {
                controller.close()
              } catch (error) {
                // Controller already closed
              }
            }, 1000)
          } catch (error) {
            console.error('Error sending timeout notification:', error)
            sseConnections.delete(operationId)
          }
        }, timeoutDuration)

        // Handle client disconnect
        request.signal.addEventListener('abort', () => {
          clearInterval(keepAliveInterval)
          clearTimeout(analysisTimeout)
          sseConnections.delete(operationId)
          console.log(`🔌 SSE connection aborted for operation ${operationId}`)
          try {
            controller.close()
          } catch (error) {
            // Controller already closed
          }
        })
      },
      cancel() {
        // Clean up when stream is cancelled
        sseConnections.delete(operationId)
      }
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      },
    })

  } catch (error: any) {
    console.error('Error creating SSE stream:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 })
  }
}

// Helper function to send notification to specific operation
export function notifyOperation(operationId: string, data: any) {
  console.log(`🔍 Looking for SSE connection for operation ${operationId}`)
  console.log(`📊 Active SSE connections: ${sseConnections.size}`)
  console.log(`📋 Connection IDs: [${Array.from(sseConnections.keys()).join(', ')}]`)

  const controller = sseConnections.get(operationId)
  if (controller) {
    try {
      const message = {
        type: 'analysis_complete',
        operationId,
        ...data
      }

      console.log(`📤 Sending SSE message for ${operationId}:`, {
        type: message.type,
        success: message.success,
        hasMarkdown: !!message.markdownContent,
        hasDocxUrl: !!message.output_file_blob_sas_url
      })

      controller.enqueue(`data: ${JSON.stringify(message)}\n\n`)
      console.log(`✅ SSE message sent successfully for ${operationId}`)

      // Close the connection after sending the completion notification
      setTimeout(() => {
        sseConnections.delete(operationId)
        try {
          controller.close()
          console.log(`🔌 SSE connection closed for ${operationId}`)
        } catch (error) {
          // Controller already closed
        }
      }, 1000)
    } catch (error) {
      console.error(`❌ Error sending SSE notification for ${operationId}:`, error)
      sseConnections.delete(operationId)
    }
  } else {
    console.warn(`⚠️  No SSE connection found for operation ${operationId}`)
    console.warn(`💡 This could mean the user closed the browser or the connection timed out`)
  }
}
