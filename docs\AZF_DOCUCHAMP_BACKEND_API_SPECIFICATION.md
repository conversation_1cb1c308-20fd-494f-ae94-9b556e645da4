# Document Analysis API Specification

This API provides asynchronous document analysis using Azure Durable Functions. Users upload a document and specify analysis parameters; the API returns a status URL to poll for results.


## Base URL

```
https://azf-docuchamp.azurewebsites.net
```


## Authentication

This API uses Azure Functions' built-in function-level authentication. Each request must include the function key as a URL suffix parameter: `?code=<your-function-key>`.

- **URL Suffix:** `?code=<your-function-key>`
- You can obtain the function key from the Azure Portal under your Function App > Functions > (select function) > Function Keys.
- Requests without a valid key will receive a `401 Unauthorized` response. you should find the key in .env file

Example:

```http
POST /api/orchestrators/analyze_document_orchestrator?code=<your-function-key> HTTP/1.1
Host: azf-docuchamp.azurewebsites.net
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary
```

## Endpoints

### 1. Start Document Analysis

**POST** `/api/orchestrators/analyze_document_orchestrator`

#### Description

Starts a new document analysis orchestration. Accepts a file upload and analysis parameters. Returns a status URL for polling the analysis result.

#### Request

- **Content-Type:** `multipart/form-data`
- **Form Data:**
    - `userId` (string, required): Unique identifier for the user.
    - `operationId` (string, required): Unique identifier for the operation.
    - `file` (required): PDF file to analyze.
    - `analysis_type` (string, required): Type of analysis. Valid values: `garment-bom`, `legal`, `education`, `finance`, `office`, `custom`.
    - `sections` (string, optional): Comma-separated list of sections to analyze (e.g., `executive_summary,financial_analysis`).
    - `language` (string, optional): Language for analysis (default: `English`).
    - `report_length` (string, optional): Report length (`short` or `long`).
    - `include_docx` (boolean, optional): Whether to include a DOCX output (`true` or `false`).
    - `analyze_complete_webhook_url` (string, required): Full webhook URL of the client application for webhook callbacks (e.g., `https://app.docuchampai.com/api/webhooks/analysis-complete`).

#### Example Request

```http
POST /api/orchestrators/analyze_document_orchestrator HTTP/1.1
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="example.pdf"
Content-Type: application/pdf

(binary file data)
------WebKitFormBoundary
Content-Disposition: form-data; name="analysis_type"

finance
------WebKitFormBoundary
Content-Disposition: form-data; name="sections"

executive_summary,financial_analysis
------WebKitFormBoundary
Content-Disposition: form-data; name="language"

English
------WebKitFormBoundary
Content-Disposition: form-data; name="report_length"

long
------WebKitFormBoundary
Content-Disposition: form-data; name="include_docx"

true
------WebKitFormBoundary
Content-Disposition: form-data; name="analyze_complete_webhook_url"

https://app.docuchampai.com/api/webhooks/analysis-complete
------WebKitFormBoundary--
```

#### Responses

- **202 Accepted**

    ```json
    {
      "id": "<instance_id>",
      "statusQueryGetUri": "<status_url>",
      "sendEventPostUri": "<event_url>",
      "terminatePostUri": "<terminate_url>",
      "purgeHistoryDeleteUri": "<purge_url>",
      "restartPostUri": "<restart_url>"
    }
    ```

- **400 Bad Request**

    ```
    No file provided
    ```

- **500 Internal Server Error**

    ```
    Error uploading file: <error details>
    ```

---

### 2. Poll Analysis Status

**GET** `{statusQueryGetUri}`

#### Description

Poll the status URL returned from the initial request to check the analysis progress or retrieve the result.

#### Response

- **200 OK**

    ```json
    {
      "runtimeStatus": "Completed",
      "input": { ... },
      "output": {
        "success": true,
        "markdownContent": "# 📊 AI Analysis Summary\n📄 Document: sample.pdf\n🎯 Use Case: Document Analysis\n📑 Pages: 5\n📦 Chunks: 2\n🖼️ Image Pages: 3\n📝 Text Pages: 2\n🎯 Tokens: 15,234\n💰 Cost: $0.0456\n⏱️ Time: 12.3s\n\n---\n\n# Executive Summary\n\nThis document provides...\n\n## Key Findings\n\n• Finding 1\n• Finding 2\n\n## Recommendations\n\n1. Recommendation 1\n2. Recommendation 2",
        "metadata": {
          "fileName": "sample.pdf",
          "fileSize": 2048576,
          "analysisType": "finance",
          "sections": ["executive_summary", "key_findings", "recommendations"],
          "language": "English",
          "reportLength": "long",
          "processedAt": "2024-01-15T10:30:45.123Z",
          "processingTimeMs": 12345,
          "pageCount": 5,
          "wordCount": 1250,
          "hasDocx": true,
          "costBreakdown": {
            "input_tokens": 12000,
            "output_tokens": 3234,
            "total_cost": 0.0456,
            "model_used": "gpt-4o"
          }
        },
        "output_file_blob_sas_url": "https://storage.blob.core.windows.net/container/sample-ai-analyzed.docx?sv=2022-11-02&ss=b&srt=sco&sp=r&se=2024-03-15T10:30:45Z&st=2024-01-15T10:30:45Z&spr=https&sig=..."
      },
      ...
    }
    ```

    - `runtimeStatus`: One of `Running`, `Completed`, `Failed`, `Terminated`.
    - `output`: The result of the analysis if completed, or error details if failed.

#### Successful Response Structure

When the analysis is completed successfully, the `output` field contains:

- **success** (boolean): Always `true` for successful responses
- **markdownContent** (string): The complete analysis report in Markdown format
- **metadata** (object): Contains all document and processing information:
  - **fileName** (string): Original filename of the uploaded document
  - **fileSize** (number): File size in bytes
  - **analysisType** (string): Type of analysis performed (e.g., "finance", "legal")
  - **sections** (array): List of sections that were analyzed
  - **language** (string): Language used for the analysis
  - **reportLength** (string): Length of the report ("short" or "long")
  - **processedAt** (string): ISO 8601 timestamp when processing completed
  - **processingTimeMs** (number): Total processing time in milliseconds
  - **pageCount** (number): Number of pages in the document
  - **wordCount** (number): Number of words in the generated report
  - **hasDocx** (boolean): Whether a DOCX file was generated
  - **costBreakdown** (object): Detailed cost information:
    - **input_tokens** (number): Number of input tokens used
    - **output_tokens** (number): Number of output tokens generated
    - **total_tokens** (number): Total tokens used
    - **input_cost** (number): Cost for input tokens
    - **output_cost** (number): Cost for output tokens
    - **total_cost** (number): Total cost for the analysis
    - **model_used** (string): AI model used for analysis
    - **input_rate_per_1k** (number): Rate per 1000 input tokens
    - **output_rate_per_1k** (number): Rate per 1000 output tokens
- **output_file_blob_sas_url** (string, optional): SAS URL for downloading the DOCX file (only present if hasDocx is true)

#### Analysis Types

The API supports the following analysis types:

- **garment-bom**: Garment BOM Factory Readiness - Analyze garment specifications for production readiness
- **legal**: Legal Document Validation - Review legal documents for compliance
- **education**: Education Content Validation - Validate educational materials and assignments
- **finance**: Finance Report Analysis - Analyze financial reports and statements
- **office**: Office Document Analysis - General document analysis for workflows
- **custom**: Custom Analysis - User-defined analysis criteria

Each analysis type has its own set of default and optional sections that can be specified in the `sections` parameter.

---

## Error Handling

- If the file is missing, returns `400 Bad Request`.
- If an internal error occurs (e.g., blob upload fails), returns `500 Internal Server Error` with details.
- If orchestration or activity fails, the `output` field in the status response will contain error details.

---

## Example Workflow

1. **POST** file and parameters to `/api/orchestrators/analyze_document_orchestrator`.
2. Receive a `202 Accepted` response with a `statusQueryGetUri`.
3. **GET** the `statusQueryGetUri` periodically until `runtimeStatus` is `Completed` or `Failed`.
4. Retrieve results from the `output` field.

## Webhook Callback

When the analysis is complete, the Azure Function should send a webhook callback to the client:

**Webhook URL**: The full URL provided in the `analyze_complete_webhook_url` parameter

**Method**: POST

**Headers**:
- `Content-Type: application/json`
- `Authorization: Bearer {webhook_secret}` (use the secret configured in the Azure Function)

**Body**:
```json
{
  "operationId": "string",
  "userId": "string",
  "status": "completed|failed",
  "fileLink": "string",
  "error": "string",
  "metadata": {}
}
```

---

## Notes

- All processing is asynchronous.
- Only metadata and blob references are passed between functions; files are stored in Azure Blob Storage.
- The API is designed for use with Azure Functions and Durable Functions.
- The `analyze_complete_webhook_url` parameter should contain the full webhook URL for callback notifications.

---
