import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/components/providers/session-provider";
import { NotificationProvider } from "@/components/providers/notification-provider";
import { LanguageProvider } from "@/contexts/language-context";
import { Header } from "@/components/layout/header";
import { ConditionalFooter } from "@/components/layout/conditional-footer";
import { ConditionalLayout } from "@/components/layout/conditional-layout";
import { GoogleAnalytics } from "@/components/analytics/google-analytics";
import { seoConfig, structuredData } from "@/lib/seo-config";


const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  metadataBase: new URL(seoConfig.siteUrl),
  title: {
    default: seoConfig.defaultTitle,
    template: seoConfig.titleTemplate
  },
  description: seoConfig.defaultDescription,
  keywords: seoConfig.defaultKeywords,
  authors: [{ name: 'DocuChampAI Team' }],
  creator: 'DocuChampAI',
  publisher: 'DocuChampAI',
  category: 'Business Software',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: seoConfig.openGraph.type,
    locale: seoConfig.openGraph.locale,
    url: seoConfig.siteUrl,
    title: seoConfig.defaultTitle,
    description: seoConfig.defaultDescription,
    siteName: seoConfig.openGraph.siteName,
    images: [
      {
        url: `${seoConfig.siteUrl}${seoConfig.openGraph.images[0].url}`,
        width: seoConfig.openGraph.images[0].width,
        height: seoConfig.openGraph.images[0].height,
        alt: seoConfig.openGraph.images[0].alt,
      }
    ],
  },
  twitter: {
    card: seoConfig.twitter.cardType,
    title: seoConfig.defaultTitle,
    description: seoConfig.defaultDescription,
    images: [`${seoConfig.siteUrl}${seoConfig.openGraph.images[0].url}`],
    creator: seoConfig.twitter.handle,
    site: seoConfig.twitter.site,
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    yandex: process.env.YANDEX_VERIFICATION,
  },
  alternates: {
    canonical: seoConfig.siteUrl,
  },
  other: {
    'google-site-verification': process.env.GOOGLE_SITE_VERIFICATION || '',
    'msvalidate.01': process.env.BING_VERIFICATION || '',
    'yandex-verification': process.env.YANDEX_VERIFICATION || '',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData.softwareApplication) }}
        />
        <link rel="canonical" href={seoConfig.siteUrl} />
        {/* Additional meta tags from config */}
        {seoConfig.additionalMetaTags.map((tag, index) => (
          <meta key={index} name={tag.name} content={tag.content} />
        ))}

        {/* Favicons */}
        <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.webp" sizes="180x180" />
        <link rel="icon" type="image/webp" href="/favicon-32x32.webp" sizes="32x32" />
        <link rel="icon" type="image/webp" href="/favicon-16x16.webp" sizes="16x16" />

        {/* PWA */}
        <link rel="manifest" href="/manifest.json" />

        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* DNS Prefetch */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      </head>
      <body className={`${inter.variable} font-sans antialiased`} suppressHydrationWarning={true}>
        <GoogleAnalytics />
        <LanguageProvider>
          <AuthProvider>
            <NotificationProvider>
              <div className="min-h-screen flex flex-col">
                <Header />
                <ConditionalLayout>
                  {children}
                </ConditionalLayout>
                <ConditionalFooter />
              </div>
            </NotificationProvider>
          </AuthProvider>
        </LanguageProvider>
      </body>
    </html>
  );
}
