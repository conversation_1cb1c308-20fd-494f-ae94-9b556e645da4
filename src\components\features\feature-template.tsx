'use client'

import React, { useState } from 'react'
import { useSession } from 'next-auth/react'
// import { Button } from '@/components/ui/button' // Commented out unused import
import { Alert, AlertDescription } from '@/components/ui/alert'
import { getCreditCost } from '@/lib/credits'
import { <PERSON>ert<PERSON>riangle, CreditCard } from 'lucide-react'

interface FeatureTemplateProps {
  featureName: string
  featureKey: keyof typeof import('@/lib/credits').CREDIT_COSTS
  description: string
  children: React.ReactNode
  onProcess: (data: unknown) => Promise<unknown>
  className?: string
}

export function FeatureTemplate({
  featureName,
  featureKey,
  description,
  children,
  onProcess,
  className = '',
}: FeatureTemplateProps) {
  const { data: session } = useSession()
  const [processing, setProcessing] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [credits, setCredits] = useState<number | null>(null)

  const creditCost = getCreditCost(featureKey)

  // Fetch user credits on component mount
  React.useEffect(() => {
    const userId = (session?.user as any)?.id
    if (userId) {
      fetchCredits()
    }
  }, [session])

  const fetchCredits = async () => {
    try {
      const response = await fetch('/api/credits/balance')
      if (response.ok) {
        const data = await response.json()
        setCredits(data.balance)
      }
    } catch (error) {
      console.error('Error fetching credits:', error)
    }
  }

  const handleProcess = async (data: unknown) => {
    const userId = (session?.user as any)?.id
    if (!userId) {
      setError('Please sign in to use this feature')
      return
    }

    if (credits !== null && credits < creditCost) {
      setError(`Insufficient credits. This feature requires ${creditCost} credits, but you have ${credits}.`)
      return
    }

    setProcessing(true)
    setError(null)
    setResult(null)

    try {
      const result = await onProcess(data)
      setResult(result)
      
      // Update credits after successful operation
      if (credits !== null) {
        setCredits(credits - creditCost)
      }
    } catch (error: unknown) {
      if (error instanceof Error && error.name === 'InsufficientCreditsError') {
        setError(error.message)
      } else {
        setError('An error occurred while processing. Please try again.')
      }
      console.error('Feature processing error:', error)
    } finally {
      setProcessing(false)
    }
  }

  return (
    <div className={`max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8 ${className}`}>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{featureName}</h1>
        <p className="text-gray-600">{description}</p>
        
        {/* Credit Info */}
        <div className="mt-4 flex items-center space-x-4">
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <CreditCard className="w-4 h-4" />
            <span>Cost: {creditCost} credits</span>
          </div>
          {credits !== null && (
            <div className="flex items-center space-x-2 text-sm">
              <span className="text-gray-500">Your balance:</span>
              <span className={`font-medium ${credits >= creditCost ? 'text-green-600' : 'text-red-600'}`}>
                {credits} credits
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Insufficient Credits Warning */}
      {credits !== null && credits < creditCost && (
        <Alert className="mb-6">
          <CreditCard className="h-4 w-4" />
          <AlertDescription>
            You need {creditCost - credits} more credits to use this feature.{' '}
            <a href="/pricing#credits" className="underline text-orange-600 hover:text-orange-700">
              Purchase credits
            </a>
          </AlertDescription>
        </Alert>
      )}

      {/* Feature Content */}
      <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
        {React.cloneElement(children as React.ReactElement<any>, {
          onSubmit: handleProcess,
          processing,
          disabled: processing || (credits !== null && credits < creditCost),
        })}
      </div>

      {/* Results */}
      {result && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Results</h2>
          <div className="prose max-w-none">
            {typeof result === 'string' ? (
              <p className="whitespace-pre-wrap">{result}</p>
            ) : (
              <pre className="bg-gray-50 p-4 rounded-md overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
