'use client'

import { useState } from 'react'
import { signIn } from 'next-auth/react'
import { useRouter } from 'next/navigation'
// import Link from 'next/link' // Commented out unused import
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useTranslation } from '@/hooks/use-translation'

interface SignInFormProps {
  onSuccess?: () => void
  onOAuthStart?: () => void
}

export function SignInForm({ onSuccess, onOAuthStart }: SignInFormProps = {}) {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()
  const { t } = useTranslation('common')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      })

      if (result?.error) {
        if (result.error === 'EMAIL_NOT_VERIFIED') {
          setError(`**${t('status.error')}:** ${t('auth.emailNotVerified')}`)
        } else {
          setError(`**${t('status.error')}:** ${t('auth.signInFailed')}`)
        }
      } else {
        onSuccess?.()
        router.push('/features/document-analysis')
        router.refresh()
      }
    } catch {
      setError(`**${t('status.error')}:** ${t('auth.error')}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Social Sign In */}
      <div className="space-y-3">
        <Button
          type="button"
          variant="outline"
          className="w-full flex items-center justify-center gap-3 py-6"
          onClick={async () => {
            onOAuthStart?.()
            try {
              const result = await signIn('google', {
                callbackUrl: '/auth/callback',
                redirect: false
              })

              if (result?.error) {
                console.error('Google sign-in error:', result.error)
                setError(`**${t('status.error')}:** ${t('auth.googleSignInFailed')}`)
              } else if (result?.url) {
                // Successful sign-in, redirect
                window.location.href = result.url
              }
            } catch (error) {
              console.error('Google OAuth error:', error)
              setError(`**${t('status.error')}:** ${t('auth.googleSignInFailed')}`)
            }
          }}
        >
          <svg className="w-5 h-5" viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          {t('auth.continueWithGoogle')}
        </Button>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-white px-2 text-gray-500">{t('auth.or')}</span>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertDescription>
              <div dangerouslySetInnerHTML={{
                __html: error.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
              }} />
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <Label htmlFor="email">{t('forms.email')}</Label>
          <Input
            id="email"
            type="email"
            placeholder={t('auth.enterEmail')}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="password">{t('forms.password')}</Label>
          <Input
            id="password"
            type="password"
            placeholder={t('auth.enterPassword')}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
          />
        </div>

        <Button type="submit" className="w-full text-white transition-all duration-300 transform hover:scale-105 !rounded-md bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 shadow-lg hover:shadow-purple-500/50 !rounded-md hover:!rounded-md" disabled={isLoading}>
          {isLoading ? t('auth.signingIn') : t('navigation.signIn')}
        </Button>
      </form>
    </div>
  )
}
