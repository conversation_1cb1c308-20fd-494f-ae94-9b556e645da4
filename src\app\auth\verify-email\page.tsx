'use client'

import { useEffect, useState, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, XCircle, Loader2 } from 'lucide-react'

function VerifyEmailContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get('token')
  const email = searchParams.get('email')

  const [isLoading, setIsLoading] = useState(true)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [signingIn, setSigningIn] = useState(false)

  useEffect(() => {
    const verifyEmail = async () => {
      if (!token || !email) {
        setError('Invalid verification link. Please check your email and try again.')
        setIsLoading(false)
        return
      }

      try {
        setIsLoading(true)
        setError(null)

        // Call the new email verification API route
        const response = await fetch(`/api/auth/verify-email?token=${encodeURIComponent(token)}&email=${encodeURIComponent(email)}`)
        const data = await response.json()

        if (!response.ok) {
          if (data.code === 'TOKEN_EXPIRED') {
            setError('This verification link has expired. Please request a new one.')
          } else if (data.code === 'INVALID_TOKEN') {
            setError('Invalid verification link. Please check your email and try again.')
          } else {
            setError(data.error || 'Verification failed. Please try again.')
          }
          setIsLoading(false)
          return
        }

        // Verification successful
        setSuccess(true)

        // Check if we have a sign-in token for automatic login
        if (data.signInToken) {
          // Show green tick for 1.5 seconds before starting sign-in process
          setTimeout(async () => {
            setSigningIn(true)

            // Import signIn dynamically to avoid SSR issues
            const { signIn } = await import('next-auth/react')

            try {
              const signInResult = await signIn('credentials', {
                email: email,
                password: 'auto-signin-token:' + data.signInToken,
                redirect: false,
              })

              if (signInResult?.ok) {
                // Successful auto sign-in
                if (data.isNewUser) {
                  // Mark as new user and clear any existing tutorial state for fresh start
                  sessionStorage.setItem('isNewUser', 'true')
                  localStorage.removeItem('docuchampai_tutorial_state')
                  console.log('✅ New user verified via email link - will show welcome message and tutorial')
                }

                // Redirect to document analysis page after a brief delay
                setTimeout(() => {
                  const redirectUrl = data.isNewUser
                    ? '/document-analysis?newUser=true'
                    : '/document-analysis'
                  router.push(redirectUrl)
                }, 1500)
              } else {
                console.error('Auto sign-in failed:', signInResult?.error)
                setSigningIn(false)
                // Fall back to manual sign-in redirect
                setTimeout(() => {
                  const redirectUrl = data.isNewUser
                    ? '/auth/signin?verified=true&email=' + encodeURIComponent(email) + '&newUser=true'
                    : '/auth/signin?verified=true&email=' + encodeURIComponent(email)
                  router.push(redirectUrl)
                }, 2000)
              }
            } catch (signInError) {
              console.error('Auto sign-in error:', signInError)
              setSigningIn(false)
              // Fall back to manual sign-in redirect
              setTimeout(() => {
                const redirectUrl = data.isNewUser
                  ? '/auth/signin?verified=true&email=' + encodeURIComponent(email) + '&newUser=true'
                  : '/auth/signin?verified=true&email=' + encodeURIComponent(email)
                router.push(redirectUrl)
              }, 2000)
            }
          }, 1500) // Show green tick for 1.5 seconds first
        } else {
          // No auto sign-in token, redirect to manual sign-in
          setTimeout(() => {
            router.push('/auth/signin?verified=true&email=' + encodeURIComponent(email))
          }, 3000)
        }

      } catch (error) {
        console.error('Email verification error:', error)
        setError('An error occurred during verification. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    verifyEmail()
  }, [token, email, router])



  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8 p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
            <h2 className="mt-6 text-2xl font-bold text-gray-900">Verifying your email...</h2>
            <p className="mt-2 text-gray-600">Please wait while we verify your email address.</p>
          </div>
        </div>
      </div>
    )
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8 p-8">
          <div className="text-center space-y-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
              {signingIn ? (
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
              ) : (
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              )}
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Email Verified Successfully!</h2>
              <p className="text-gray-600 mt-2">
                {signingIn
                  ? 'Your email has been verified. Signing you in...'
                  : 'Your email has been verified. You can now sign in to your DocuChampAI account.'
                }
              </p>
              {!signingIn && (
                <p className="text-sm text-gray-500 mt-4">
                  You&apos;ll be redirected to the sign-in page in a few seconds...
                </p>
              )}
            </div>
            <Button
              onClick={() => router.push('/auth/signin?verified=true&email=' + encodeURIComponent(email || ''))}
              className="w-full bg-purple-600 hover:bg-purple-700"
            >
              Continue to Sign In
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center space-y-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Verification Failed</h2>
            {error && (
              <Alert variant="destructive" className="mt-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </div>
          <div className="space-y-3">
            <Button
              onClick={() => router.push('/auth/signup')}
              className="w-full bg-purple-600 hover:bg-purple-700"
            >
              Back to Sign Up
            </Button>
            <Button
              onClick={() => router.push('/auth/signin')}
              variant="outline"
              className="w-full"
            >
              Sign In Instead
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function VerifyEmailPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8 p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
            <h2 className="mt-6 text-2xl font-bold text-gray-900">Loading...</h2>
          </div>
        </div>
      </div>
    }>
      <VerifyEmailContent />
    </Suspense>
  )
}
