import { prisma } from './prisma'
import { addMonths, isAfter } from 'date-fns'
import { stripe } from './stripe'
import { env } from './config'

export interface CreditRenewalResult {
  userId: string
  previousCredits: number
  newCredits: number
  monthlyAllowance: number
  nextRenewalDate: Date
}

/**
 * Set up monthly credit renewal for a user based on Stripe subscription
 */
export async function setupMonthlyCreditRenewal(
  userId: string,
  monthlyCredits: number,
  stripeSubscriptionId?: string
): Promise<void> {
  if (!env.ENABLE_CREDIT_MANAGEMENT) {
    console.log('Credit management is disabled')
    return
  }

  let renewalDate = addMonths(new Date(), 1) // Default fallback

  // If we have a Stripe subscription ID, get the actual renewal date from Stripe
  if (stripeSubscriptionId) {
    try {
      const subscription = await stripe.subscriptions.retrieve(stripeSubscriptionId)
      renewalDate = new Date((subscription as any).current_period_end * 1000)
    } catch (error) {
      console.error('Error fetching Stripe subscription for renewal setup:', error)
    }
  }

  await prisma.user.update({
    where: { id: userId },
    data: {
      monthlyCredits,
      creditsRenewAt: renewalDate,
      lastCreditRenewal: new Date(),
      credits: monthlyCredits, // Set initial credits
    },
  })
}

/**
 * Renew credits for a specific user based on their Stripe subscription
 */
export async function renewUserCredits(userId: string): Promise<CreditRenewalResult | null> {
  if (!env.ENABLE_CREDIT_MANAGEMENT) {
    console.log('Credit management is disabled')
    return null
  }

  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      subscriptions: {
        where: { status: 'active' }
      }
    }
  })

  if (!user || !user.creditsRenewAt || !user.monthlyCredits) {
    return null
  }

  // Check if renewal is due
  if (!isAfter(new Date(), user.creditsRenewAt)) {
    return null
  }

  // Get the active subscription to sync with Stripe
  const activeSubscription = user.subscriptions.find(sub => sub.status === 'active')
  let nextRenewalDate = addMonths(user.creditsRenewAt, 1) // Default fallback

  if (activeSubscription?.stripeSubscriptionId) {
    try {
      const stripeSubscription = await stripe.subscriptions.retrieve(activeSubscription.stripeSubscriptionId)
      nextRenewalDate = new Date((stripeSubscription as any).current_period_end * 1000)
    } catch (error) {
      console.error('Error fetching Stripe subscription for renewal:', error)
    }
  }

  const previousCredits = user.credits
  const newCredits = user.monthlyCredits

  // Update user with renewed credits
  await prisma.user.update({
    where: { id: userId },
    data: {
      credits: newCredits,
      creditsRenewAt: nextRenewalDate,
      lastCreditRenewal: new Date(),
    },
  })

  // Record the renewal transaction
  await prisma.transaction.create({
    data: {
      userId,
      type: 'credit_renewal',
      amount: 0, // No charge for renewal
      currency: 'HKD',
      status: 'completed',
      credits: newCredits,
      description: `Monthly credit renewal: ${newCredits} credits`,
      // Expanded metadata fields
      previousCredits,
      renewalDate: new Date().toISOString(),
      nextRenewalDate: nextRenewalDate.toISOString(),
      stripeSubscriptionId: activeSubscription?.stripeSubscriptionId || null,
      metadata: {
        previousCredits,
        renewalDate: new Date().toISOString(),
        nextRenewalDate: nextRenewalDate.toISOString(),
        stripeSubscriptionId: activeSubscription?.stripeSubscriptionId || null,
      },
    },
  })

  return {
    userId,
    previousCredits,
    newCredits,
    monthlyAllowance: user.monthlyCredits,
    nextRenewalDate,
  }
}

/**
 * Check and renew credits for all users who are due for renewal
 */
export async function processAllCreditRenewals(): Promise<CreditRenewalResult[]> {
  if (!env.ENABLE_CREDIT_MANAGEMENT) {
    console.log('Credit management is disabled')
    return []
  }

  const now = new Date()

  // Find users whose credits are due for renewal
  const usersForRenewal = await prisma.user.findMany({
    where: {
      creditsRenewAt: {
        lte: now,
      },
      monthlyCredits: {
        gt: 0,
      },
    },
    include: {
      subscriptions: {
        where: { status: 'active' }
      }
    }
  })

  const results: CreditRenewalResult[] = []

  for (const user of usersForRenewal) {
    const result = await renewUserCredits(user.id)
    if (result) {
      results.push(result)
    }
  }

  return results
}

/**
 * Sync credit renewal dates with Stripe subscription renewals
 * This should be called when a subscription is renewed in Stripe
 */
export async function syncCreditRenewalWithStripe(
  userId: string,
  stripeSubscriptionId: string
): Promise<void> {
  if (!env.ENABLE_CREDIT_MANAGEMENT) {
    return
  }

  try {
    const subscription = await stripe.subscriptions.retrieve(stripeSubscriptionId)
    const nextRenewalDate = new Date((subscription as any).current_period_end * 1000)

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { monthlyCredits: true }
    })

    if (user && user.monthlyCredits > 0) {
      await prisma.user.update({
        where: { id: userId },
        data: {
          creditsRenewAt: nextRenewalDate,
        },
      })

      console.log(`Synced credit renewal date for user ${userId} with Stripe subscription ${stripeSubscriptionId}`)
    }
  } catch (error) {
    console.error('Error syncing credit renewal with Stripe:', error)
  }
}

/**
 * Get user's credit renewal information
 */
export async function getUserCreditRenewalInfo(userId: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      credits: true,
      monthlyCredits: true,
      creditsRenewAt: true,
      lastCreditRenewal: true,
    },
  })

  if (!user) {
    return null
  }

  const hasMonthlyRenewal = user.monthlyCredits > 0
  const daysUntilRenewal = user.creditsRenewAt 
    ? Math.ceil((user.creditsRenewAt.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    : null

  return {
    currentCredits: user.credits,
    monthlyAllowance: user.monthlyCredits,
    hasMonthlyRenewal,
    nextRenewalDate: user.creditsRenewAt,
    daysUntilRenewal,
    lastRenewalDate: user.lastCreditRenewal,
  }
}

/**
 * Cancel monthly credit renewal for a user
 */
export async function cancelMonthlyCreditRenewal(userId: string): Promise<void> {
  await prisma.user.update({
    where: { id: userId },
    data: {
      monthlyCredits: 0,
      creditsRenewAt: null,
    },
  })
}

/**
 * Debug function to test credit renewal without any conditions
 * This bypasses all normal checks and forces a credit renewal
 */
export async function debugRenewUserCredits(userId: string): Promise<CreditRenewalResult | null> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      subscriptions: {
        where: { status: 'active' }
      }
    }
  })

  if (!user) {
    return null
  }

  // Try to get credits from active subscription first
  let monthlyCredits = user.monthlyCredits || 0

  // If no monthly credits set, try to get from active subscription
  if (monthlyCredits === 0 && user.subscriptions.length > 0) {
    const activeSubscription = user.subscriptions[0]
    if (activeSubscription.productName) {
      // Import config to get credit packages
      const { getCreditPackages } = await import('./config')
      const creditPackages = getCreditPackages()
      const creditPackage = creditPackages.find(pkg => pkg.name === activeSubscription.productName)
      if (creditPackage) {
        monthlyCredits = creditPackage.credits
      }
    }
  }

  // Final fallback to 300 for testing if still no credits found
  if (monthlyCredits === 0) {
    monthlyCredits = 300
  }

  const previousCredits = user.credits
  const nextRenewalDate = addMonths(new Date(), 1)

  // Update user with renewed credits
  await prisma.user.update({
    where: { id: userId },
    data: {
      credits: monthlyCredits,
      creditsRenewAt: nextRenewalDate,
      lastCreditRenewal: new Date(),
      monthlyCredits: monthlyCredits, // Ensure monthly credits is set
    },
  })

  // Record the renewal transaction
  await prisma.transaction.create({
    data: {
      userId,
      type: 'debug_credit_renewal',
      amount: 0,
      currency: 'HKD',
      status: 'completed',
      credits: monthlyCredits,
      description: `Debug credit renewal: ${monthlyCredits} credits`,
      metadata: {
        type: 'debug',
        previousCredits,
        renewalDate: new Date().toISOString(),
        nextRenewalDate: nextRenewalDate.toISOString(),
      },
    },
  })

  return {
    userId,
    previousCredits,
    newCredits: monthlyCredits,
    monthlyAllowance: monthlyCredits,
    nextRenewalDate,
  }
}

/**
 * Add one-time credits to user (doesn't affect monthly renewal)
 */
export async function addOneTimeCredits(
  userId: string,
  credits: number,
  description: string = 'One-time credit purchase'
): Promise<void> {
  await prisma.user.update({
    where: { id: userId },
    data: {
      credits: {
        increment: credits,
      },
    },
  })

  // Record the transaction
  await prisma.transaction.create({
    data: {
      userId,
      type: 'credits',
      amount: 0, // Amount would be set by payment processor
      currency: 'HKD',
      status: 'completed',
      credits,
      description,
      // Expanded metadata fields
      packageCredit: credits,
      metadata: {
        type: 'one_time_credits',
        packageCredit: credits
      },
    },
  })
}
