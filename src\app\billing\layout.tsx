import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Billing & Subscriptions',
  description: 'Manage your billing information, view subscription details, and update payment methods for your DocuChampAI account.',
  keywords: [
    'DocuChampAI billing',
    'subscription management',
    'payment methods',
    'billing history',
    'invoice management',
    'subscription plans'
  ],
  openGraph: {
    title: 'Billing & Subscriptions | DocuChampAI',
    description: 'Manage your billing information, view subscription details, and update payment methods for your DocuChampAI account.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Billing & Subscriptions | DocuChampAI',
    description: 'Manage your billing information, view subscription details, and update payment methods for your DocuChampAI account.',
  },
}

export default function BillingLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
