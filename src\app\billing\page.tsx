'use client'

import { useSession } from 'next-auth/react'
import { useEffect, useState, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { formatCurrency, formatDate } from '@/lib/utils'
import { CreditCard, AlertTriangle, CheckCircle, Check, Bug, Minus, Plus, RefreshCw, X } from 'lucide-react'
import { PaymentSuccessModal } from '@/components/onboarding/payment-success-modal'

interface UserData {
  id: string
  name: string
  email: string
  credits: number
  subscriptions: Array<{
    id: string
    status: string
    currentPeriodStart: string
    currentPeriodEnd: string
    cancelAtPeriodEnd: boolean
    product: {
      name: string
      price: number
      currency: string
      interval: string
    }
  }>
  transactions: Array<{
    id: string
    type: string
    amount: number
    currency: string
    status: string
    description: string
    createdAt: string
    credits?: number
  }>
  renewalInfo?: {
    currentCredits: number
    monthlyAllowance: number
    hasMonthlyRenewal: boolean
    nextRenewalDate: string | null
    daysUntilRenewal: number | null
    lastRenewalDate: string | null
  }
}

function BillingContent() {
  const { data: session } = useSession()
  const searchParams = useSearchParams()
  const [userData, setUserData] = useState<UserData | null>(null)
  const [loading, setLoading] = useState(true)
  const [portalLoading, setPortalLoading] = useState(false)
  const [showSuccessAlert, setShowSuccessAlert] = useState(false)
  const [showErrorAlert, setShowErrorAlert] = useState(false)
  const [debugLoading, setDebugLoading] = useState(false)
  const [debugMessage, setDebugMessage] = useState('')
  const [showPaymentSuccessModal, setShowPaymentSuccessModal] = useState(false)
  const [latestTransaction, setLatestTransaction] = useState<any>(null)

  useEffect(() => {
    const userId = (session?.user as any)?.id
    if (userId) {
      fetchUserData()
    }
  }, [session])

  useEffect(() => {
    // Check for success/error parameters
    const success = searchParams.get('success')
    const canceled = searchParams.get('canceled')

    if (success === 'true') {
      // Fetch latest transaction for payment success modal
      fetchLatestTransaction()
      setShowSuccessAlert(true)
      // Auto-hide after 5 seconds
      setTimeout(() => setShowSuccessAlert(false), 5000)
    }

    if (canceled === 'true') {
      setShowErrorAlert(true)
      // Auto-hide after 5 seconds
      setTimeout(() => setShowErrorAlert(false), 5000)
    }
  }, [searchParams])

  const fetchUserData = async () => {
    try {
      const response = await fetch('/api/user/billing')
      if (response.ok) {
        const data = await response.json()
        setUserData(data)
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchLatestTransaction = async () => {
    try {
      const response = await fetch('/api/user/latest-transaction')
      if (response.ok) {
        const data = await response.json()
        if (data.transaction) {
          setLatestTransaction(data.transaction)
          setShowPaymentSuccessModal(true)
        }
      }
    } catch (error) {
      console.error('Error fetching latest transaction:', error)
    }
  }

  const handleManageSubscription = async () => {
    setPortalLoading(true)
    try {
      const response = await fetch('/api/billing/portal', {
        method: 'POST',
      })

      if (response.ok) {
        const { url } = await response.json()
        window.location.href = url
      } else {
        console.error('Failed to create portal session')
      }
    } catch (error) {
      console.error('Error creating portal session:', error)
    } finally {
      setPortalLoading(false)
    }
  }

  const handleDebugAction = async (action: string, amount?: number) => {
    setDebugLoading(true)
    setDebugMessage('')

    try {
      const response = await fetch('/api/debug/credits', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, amount }),
      })

      const data = await response.json()

      if (response.ok) {
        setDebugMessage(data.message)
        // Refresh user data to show updated credits
        await fetchUserData()
      } else {
        setDebugMessage(`Error: ${data.error}`)
      }
    } catch (error) {
      setDebugMessage('Error performing debug action')
      console.error('Debug action error:', error)
    } finally {
      setDebugLoading(false)
    }
  }



  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-500"></div>
        </div>
      </ProtectedRoute>
    )
  }

  const activeSubscription = userData?.subscriptions.find(sub => sub.status === 'active')

  return (
    <ProtectedRoute>
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Billing & Subscription Management</h1>
          <p className="text-gray-600 mt-2">
            Manage your subscription, view billing history, update payment methods, and
            download invoices through Stripe&apos;s secure customer portal.
          </p>
        </div>

        {/* Success Alert */}
        {showSuccessAlert && (
          <Alert className="mb-6 border-green-200 bg-green-50">
            <Check className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              Payment successful! Your subscription has been activated and credits have been added to your account.
            </AlertDescription>
          </Alert>
        )}

        {/* Error Alert */}
        {showErrorAlert && (
          <Alert variant="destructive" className="mb-6">
            <X className="h-4 w-4" />
            <AlertDescription>
              Payment was canceled. If you need help with your subscription, please contact support.
            </AlertDescription>
          </Alert>
        )}

        {/* Current Subscription */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Current Subscription</h2>
          
          {activeSubscription ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Plan</label>
                  <p className="text-lg font-semibold">{activeSubscription.product.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-green-600 font-medium">Active</span>
                  </div>
                </div>
              </div>

              {activeSubscription.cancelAtPeriodEnd && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Subscription Scheduled for Cancellation
                    <br />
                    {activeSubscription.currentPeriodEnd
                      ? `Your subscription will end on ${formatDate(activeSubscription.currentPeriodEnd)}. You can reactivate it anytime before then.`
                      : 'Your subscription is scheduled for cancellation. You can reactivate it anytime.'
                    }
                  </AlertDescription>
                </Alert>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">No active subscription found</p>
              <Button asChild>
                <a href="/pricing">View Pricing Plans</a>
              </Button>
            </div>
          )}
        </div>

        {/* Credits */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Credits</h2>
          <div className="flex flex-col items-center">
            <div className="text-center">
              <p className="text-3xl font-bold text-purple-500">{userData?.credits || 0}</p>
              <p className="text-gray-500">Available Credits</p>

              {userData?.renewalInfo?.hasMonthlyRenewal && userData.renewalInfo.nextRenewalDate && (
                <p className="text-sm text-gray-600 mt-2">
                  Next renewal: {formatDate(userData.renewalInfo.nextRenewalDate)}
                  {userData.renewalInfo.daysUntilRenewal !== null && (
                    <span className="ml-2">({userData.renewalInfo.daysUntilRenewal} days)</span>
                  )}
                </p>
              )}
            </div>

            {!userData?.renewalInfo?.hasMonthlyRenewal && (
              <div className="mt-4">
                <Button asChild variant="outline">
                  <a href="/pricing">Purchase Credits</a>
                </Button>
              </div>
            )}
          </div>
        </div>



        {/* Debug Tools - Only show when debug tools are enabled */}
        {process.env.NEXT_PUBLIC_ENABLE_DEBUG_TOOLS === 'true' && (
          <div className="bg-yellow-50 rounded-lg shadow-sm border border-yellow-200 p-6 mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <Bug className="w-6 h-6 text-yellow-600" />
              <h2 className="text-xl font-semibold text-gray-900">Debug Tools</h2>
            </div>
            <p className="text-gray-600 mb-4">
              Development tools for testing credit management functionality.
            </p>

            {debugMessage && (
              <Alert className="mb-4">
                <AlertDescription>{debugMessage}</AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button
                onClick={() => handleDebugAction('deduct', 1)}
                disabled={debugLoading}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <Minus className="w-4 h-4" />
                <span>Deduct 1 Credit</span>
              </Button>

              <Button
                onClick={() => handleDebugAction('deduct', 10)}
                disabled={debugLoading}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <Minus className="w-4 h-4" />
                <span>Deduct 10 Credits</span>
              </Button>

              <Button
                onClick={() => handleDebugAction('deduct', 50)}
                disabled={debugLoading}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <Minus className="w-4 h-4" />
                <span>Deduct 50 Credits</span>
              </Button>

              <Button
                onClick={() => handleDebugAction('add', 50)}
                disabled={debugLoading}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <Plus className="w-4 h-4" />
                <span>Add 50 Credits</span>
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-1 gap-4 mt-4">
              <Button
                onClick={() => handleDebugAction('renew')}
                disabled={debugLoading}
                variant="outline"
                className="flex items-center justify-center space-x-2"
              >
                <RefreshCw className="w-4 h-4" />
                <span>Test Credit Renewal</span>
              </Button>
            </div>

            {debugLoading && (
              <p className="text-sm text-gray-500 mt-2">Processing debug action...</p>
            )}
          </div>
        )}

        {/* Stripe Customer Portal */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <CreditCard className="w-6 h-6 text-purple-500" />
            <h2 className="text-xl font-semibold text-gray-900">Stripe Customer Portal</h2>
          </div>
          <p className="text-gray-600 mb-4">
            Access your complete billing dashboard with subscription management, payment
            history and invoice downloads.
          </p>
          <Button
            onClick={handleManageSubscription}
            disabled={portalLoading}
            className="bg-purple-500 hover:bg-purple-600"
          >
            {portalLoading ? 'Loading...' : 'Manage Subscription & Billing'}
          </Button>
          <p className="text-sm text-gray-500 mt-2">
            You&apos;ll be redirected to Stripe&apos;s secure portal where you can:
          </p>
        </div>

        {/* Recent Transactions */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Transactions</h2>
          
          {userData?.transactions && userData.transactions.length > 0 ? (
            <div className="space-y-4">
              {userData.transactions.slice(0, 5).map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between py-3 border-b last:border-b-0">
                  <div>
                    <p className="font-medium text-gray-900">{transaction.description}</p>
                    <p className="text-sm text-gray-500">
                      {formatDate(transaction.createdAt)} • {transaction.type}
                      {transaction.credits && ` • ${transaction.credits} credits`}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-gray-900">
                      {formatCurrency(transaction.amount / 100, transaction.currency)}
                    </p>
                    <p className={`text-sm ${
                      transaction.status === 'completed' ? 'text-green-600' : 'text-yellow-600'
                    }`}>
                      {transaction.status}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-8">No transactions found</p>
          )}
        </div>
      </div>

      {/* Payment Success Modal */}
      {latestTransaction && (
        <PaymentSuccessModal
          isOpen={showPaymentSuccessModal}
          onClose={() => setShowPaymentSuccessModal(false)}
          creditsAdded={latestTransaction.credits || 0}
          packageName={latestTransaction.packageName || 'Credits'}
          transactionType={latestTransaction.type}
        />
      )}

    </ProtectedRoute>
  )
}

export default function BillingPage() {
  return (
    <Suspense fallback={<div className="p-8">Loading...</div>}>
      <BillingContent />
    </Suspense>
  )
}
