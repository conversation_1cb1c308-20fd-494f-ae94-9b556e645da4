import { hash, compare } from 'bcryptjs'
import { sign, verify } from 'jsonwebtoken'
import { env } from './config'

export async function hashPassword(password: string): Promise<string> {
  return hash(password, 12)
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return compare(password, hashedPassword)
}

export function generateToken(payload: any, expiresIn: string | number = '24h'): string {
  const secret = env.JWT_SECRET || 'fallback-secret-for-development'
  return sign(payload, secret, { expiresIn: expiresIn as any })
}

export function verifyToken(token: string): any {
  try {
    const secret = env.JWT_SECRET || 'fallback-secret-for-development'
    return verify(token, secret)
  } catch {
    return null
  }
}

export function generateVerificationToken(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
}

export function generateResetToken(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
}
