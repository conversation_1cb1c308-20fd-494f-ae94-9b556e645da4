import { NextAuthOptions } from 'next-auth'
import { PrismaAdapter } from '@auth/prisma-adapter'
import CredentialsProvider from 'next-auth/providers/credentials'
import GoogleProvider from 'next-auth/providers/google'
import { prisma } from './prisma'
import { compare } from 'bcryptjs'
import { env } from './config'
import { verifyToken } from './auth-utils'

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma) as any,
  session: {
    strategy: 'jwt',
  },
  pages: {
    signIn: '/auth/signin',
  },
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        // Check if this is an auto-signin token
        if (credentials.password.startsWith('auto-signin-token:')) {
          const token = credentials.password.replace('auto-signin-token:', '')

          try {
            const payload = verifyToken(token)
            if (payload && payload.purpose === 'auto_signin_after_verification' && payload.email === credentials.email) {
              const user = await prisma.user.findUnique({
                where: { id: payload.userId },
              })

              if (user && user.emailVerified && user.email === credentials.email) {
                return {
                  id: user.id,
                  email: user.email,
                  name: user.name,
                  image: user.image,
                }
              }
            }
          } catch (error) {
            console.error('Auto-signin token verification failed:', error)
            return null
          }

          return null
        }

        // Regular password authentication
        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email,
          },
        })

        if (!user || !user.password) {
          return null
        }

        // Check if email is verified
        if (!user.emailVerified) {
          throw new Error('EMAIL_NOT_VERIFIED')
        }

        const isPasswordValid = await compare(credentials.password, user.password)

        if (!isPasswordValid) {
          return null
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          image: user.image,
        }
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account }) {
      // Handle account linking for same email addresses
      if (account?.provider === 'google' && user.email) {
        try {
          const existingUser = await prisma.user.findUnique({
            where: { email: user.email },
            include: { accounts: true }
          })

          if (existingUser) {
            // Check if this Google account is already linked
            const existingGoogleAccount = existingUser.accounts.find(
              acc => acc.provider === 'google'
            )

            if (!existingGoogleAccount) {
              // Link the Google account to the existing user
              console.log(`🔗 Linking Google account to existing user: ${user.email}`)
              await prisma.account.create({
                data: {
                  userId: existingUser.id,
                  type: account.type,
                  provider: account.provider,
                  providerAccountId: account.providerAccountId,
                  refresh_token: account.refresh_token,
                  access_token: account.access_token,
                  expires_at: account.expires_at,
                  token_type: account.token_type,
                  scope: account.scope,
                  id_token: account.id_token,
                }
              })
              console.log(`✅ Successfully linked Google account for: ${user.email}`)
            } else {
              console.log(`✅ Google account already linked for: ${user.email}`)
            }

            // For SSO users, ensure emailVerified is set immediately
            if (!existingUser.emailVerified) {
              await prisma.user.update({
                where: { id: existingUser.id },
                data: { emailVerified: new Date() }
              })
              console.log(`✅ Updated emailVerified for SSO user: ${user.email}`)
            }

            // Update the user object to use the existing user's ID
            user.id = existingUser.id
          } else {
            console.log(`🆕 New Google user signing up: ${user.email}`)
          }
        } catch (error) {
          console.error('❌ Error during Google account linking:', error)
          // Allow sign-in to continue even if linking fails
          // The error will be logged but won't block the user
        }
      }
      return true
    },
    async jwt({ token, user, account }) {
      if (user) {
        token.id = user.id

        // If this is a Google sign-in, check if we need to give free credits
        if (account?.provider === 'google') {
          const dbUser = await prisma.user.findUnique({
            where: { id: user.id },
            include: { transactions: true }
          })

          if (dbUser) {
            // Ensure emailVerified is set for SSO users (for new users created by adapter)
            if (!dbUser.emailVerified) {
              await prisma.user.update({
                where: { id: user.id },
                data: { emailVerified: new Date() }
              })
              console.log(`✅ Set emailVerified for new SSO user: ${user.email}`)
            }

            // Only give credits if this is truly a new user (no previous transactions)
            if (dbUser.credits === 0 && dbUser.transactions.length === 0) {
              // Give new Google users free credits
              await prisma.user.update({
                where: { id: user.id },
                data: { credits: 10 },
              })

              // Create transaction record for free credits
              await prisma.transaction.create({
                data: {
                  userId: user.id,
                  type: 'free_credits',
                  amount: 0,
                  currency: 'HKD',
                  status: 'completed',
                  credits: 10,
                  description: 'Welcome bonus: 10 free credits (Google SSO)',
                  metadata: {
                    type: 'welcome_bonus',
                    reason: 'google_sso_signup',
                    provider: 'google'
                  }
                }
              })

              // Mark this as a new user for welcome message
              token.isNewUser = true
              console.log(`✅ New Google user granted 10 welcome credits: ${user.email}`)
            }
          }
        }
      }
      return token
    },
    async session({ session, token }) {
      if (token && session.user) {
        (session.user as any).id = token.id as string
        // Pass isNewUser flag to session for SSO users
        if (token.isNewUser) {
          (session as any).isNewUser = true
        }
      }
      return session
    },
  },
  secret: env.NEXTAUTH_SECRET,
}
