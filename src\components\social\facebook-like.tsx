'use client'

import { useEffect, useState } from 'react'

interface FacebookLikeProps {
  url?: string
  width?: number
  showFaces?: boolean
  layout?: 'standard' | 'button_count' | 'button' | 'box_count'
  action?: 'like' | 'recommend'
  size?: 'small' | 'large'
  share?: boolean
  className?: string
}

export function FacebookLike({
  url,
  width = 450,
  showFaces = true,
  layout = 'standard',
  action = 'like',
  size = 'small',
  share = true,
  className = ''
}: FacebookLikeProps) {
  const [currentUrl, setCurrentUrl] = useState('')

  useEffect(() => {
    setCurrentUrl(url || window.location.href)
  }, [url])
  useEffect(() => {
    // Load Facebook SDK
    if (typeof window !== 'undefined' && !window.FB) {
      const script = document.createElement('script')
      script.src = 'https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v18.0'
      script.async = true
      script.defer = true
      script.crossOrigin = 'anonymous'
      document.body.appendChild(script)

      // Initialize Facebook SDK when loaded
      script.onload = () => {
        if (window.FB) {
          window.FB.init({
            appId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID || '',
            xfbml: true,
            version: 'v18.0'
          })
        }
      }
    } else if (window.FB) {
      // Re-parse if SDK already loaded
      window.FB.XFBML.parse()
    }
  }, [])

  if (!currentUrl) {
    return <div className={className}>Loading...</div>
  }

  return (
    <div className={className}>
      <div
        className="fb-like"
        data-href={currentUrl}
        data-width={width}
        data-layout={layout}
        data-action={action}
        data-size={size}
        data-share={share}
        data-show-faces={showFaces}
      />
    </div>
  )
}

// Declare Facebook SDK types
declare global {
  interface Window {
    FB: {
      init: (params: any) => void
      XFBML: {
        parse: () => void
      }
    }
  }
}
