/**
 * Browser notification utilities
 */

export interface NotificationAction {
  action: string
  title: string
  icon?: string
}

export interface NotificationOptions {
  title: string
  body: string
  icon?: string
  badge?: string
  tag?: string
  requireInteraction?: boolean
  silent?: boolean
  actions?: NotificationAction[]
}

export interface NotificationPermissionResult {
  granted: boolean
  denied: boolean
  default: boolean
}

/**
 * Check if browser supports notifications
 */
export function isNotificationSupported(): boolean {
  return 'Notification' in window
}

/**
 * Get current notification permission status
 */
export function getNotificationPermission(): NotificationPermissionResult {
  if (!isNotificationSupported()) {
    return { granted: false, denied: true, default: false }
  }

  const permission = Notification.permission
  return {
    granted: permission === 'granted',
    denied: permission === 'denied',
    default: permission === 'default'
  }
}

/**
 * Request notification permission from user
 */
export async function requestNotificationPermission(): Promise<NotificationPermissionResult> {
  if (!isNotificationSupported()) {
    return { granted: false, denied: true, default: false }
  }

  try {
    const permission = await Notification.requestPermission()
    return {
      granted: permission === 'granted',
      denied: permission === 'denied',
      default: permission === 'default'
    }
  } catch (error) {
    console.error('Error requesting notification permission:', error)
    return { granted: false, denied: true, default: false }
  }
}

/**
 * Show browser notification
 */
export async function showNotification(
  title: string,
  body: string,
  options: Partial<NotificationOptions> = {}
): Promise<Notification | null> {
  if (!isNotificationSupported()) {
    console.warn('Notifications not supported in this browser')
    return null
  }

  const permission = getNotificationPermission()
  
  if (!permission.granted) {
    if (permission.default) {
      const newPermission = await requestNotificationPermission()
      if (!newPermission.granted) {
        console.warn('Notification permission denied')
        return null
      }
    } else {
      console.warn('Notification permission denied')
      return null
    }
  }

  try {
    const notification = new Notification(title, {
      body,
      icon: options.icon || '/favicon.ico',
      badge: options.badge || '/favicon.ico',
      tag: options.tag || 'docuchamp-notification',
      requireInteraction: options.requireInteraction || false,
      silent: options.silent || false,
      ...options
    })

    // Auto-close after 5 seconds unless requireInteraction is true
    if (!options.requireInteraction) {
      setTimeout(() => {
        notification.close()
      }, 5000)
    }

    return notification
  } catch (error) {
    console.error('Error showing notification:', error)
    return null
  }
}

/**
 * Show analysis completion notification
 */
export async function showAnalysisCompleteNotification(
  fileName: string,
  onClick?: () => void
): Promise<Notification | null> {
  const notification = await showNotification(
    'Analysis Complete! 🎉',
    `Your document "${fileName}" has been analyzed successfully. Click to view results.`,
    {
      tag: 'analysis-complete',
      requireInteraction: true,
      icon: '/favicon.ico'
    }
  )

  if (notification && onClick) {
    notification.onclick = () => {
      onClick()
      notification.close()
    }
  }

  return notification
}

/**
 * Show analysis failed notification
 */
export async function showAnalysisFailedNotification(
  fileName: string,
  error?: string
): Promise<Notification | null> {
  return await showNotification(
    'Analysis Failed ❌',
    `Failed to analyze "${fileName}". ${error ? `Error: ${error}` : 'Please try again.'}`,
    {
      tag: 'analysis-failed',
      requireInteraction: false,
      icon: '/favicon.ico'
    }
  )
}

/**
 * Show credit refund notification
 */
export async function showCreditRefundNotification(
  credits: number
): Promise<Notification | null> {
  return await showNotification(
    'Credit Refunded 💰',
    `${credits} credit${credits !== 1 ? 's' : ''} have been refunded to your account.`,
    {
      tag: 'credit-refund',
      requireInteraction: false,
      icon: '/favicon.ico'
    }
  )
}

/**
 * Show low credits warning notification
 */
export async function showLowCreditsNotification(
  remainingCredits: number
): Promise<Notification | null> {
  return await showNotification(
    'Low Credits Warning ⚠️',
    `You have ${remainingCredits} credit${remainingCredits !== 1 ? 's' : ''} remaining. Consider purchasing more credits.`,
    {
      tag: 'low-credits',
      requireInteraction: false,
      icon: '/favicon.ico'
    }
  )
}

/**
 * Show subscription renewal notification
 */
export async function showSubscriptionRenewalNotification(
  planName: string,
  creditsAdded: number
): Promise<Notification | null> {
  return await showNotification(
    'Subscription Renewed 🔄',
    `Your ${planName} subscription has been renewed. ${creditsAdded} credits added to your account.`,
    {
      tag: 'subscription-renewal',
      requireInteraction: false,
      icon: '/favicon.ico'
    }
  )
}

/**
 * Clear all notifications with specific tag
 */
export function clearNotificationsByTag(tag: string): void {
  // Note: There's no direct way to clear notifications by tag in the browser API
  // This is a placeholder for future implementation if needed
  console.log(`Clearing notifications with tag: ${tag}`)
}

/**
 * Initialize notification system
 */
export async function initializeNotifications(): Promise<boolean> {
  if (!isNotificationSupported()) {
    console.warn('Notifications not supported')
    return false
  }

  const permission = getNotificationPermission()
  
  if (permission.default) {
    const newPermission = await requestNotificationPermission()
    return newPermission.granted
  }

  return permission.granted
}
