import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Get the latest transaction (within last 5 minutes to ensure it's recent)
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
    
    const latestTransaction = await prisma.transaction.findFirst({
      where: {
        userId: user.id,
        createdAt: {
          gte: fiveMinutesAgo
        },
        status: 'completed',
        // Only include user-initiated payment transactions (exclude automatic renewals)
        type: {
          in: ['subscription_create', 'subscription_upgrade', 'subscription_downgrade', 'credit_purchase']
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    if (!latestTransaction) {
      return NextResponse.json({ transaction: null })
    }

    return NextResponse.json({
      transaction: {
        id: latestTransaction.id,
        type: latestTransaction.type,
        credits: latestTransaction.credits,
        packageName: latestTransaction.packageName || 'Credits',
        description: latestTransaction.description,
        createdAt: latestTransaction.createdAt
      }
    })

  } catch (error) {
    console.error('Error fetching latest transaction:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
