'use client'

import React, { useState, useRef, useCallback, useEffect } from 'react'
import { Upload, X, FileText, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { formatFileSize } from '@/lib/file-utils'
import { cn } from '@/lib/utils'
import { useTranslation } from '@/hooks/use-translation'

export interface TrialFileUploadProps {
  onFileSelect: (file: File) => void
  onFileRemove?: () => void
  selectedFile?: File | null
  disabled?: boolean
  className?: string
  trialsRemaining?: number
  canUseTrial?: boolean
}

// Trial-specific file validation (1MB limit, office documents only)
const TRIAL_MAX_FILE_SIZE = 1 * 1024 * 1024 // 1MB
const TRIAL_SUPPORTED_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'text/plain'
]

function validateTrialFile(file: File): { isValid: boolean; error?: string } {
  // Check file size
  if (file.size > TRIAL_MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: 'File too large. Trial supports files up to 1MB. Please sign up for larger files.'
    }
  }

  // Check file type
  if (!TRIAL_SUPPORTED_TYPES.includes(file.type)) {
    return {
      isValid: false,
      error: 'Unsupported file type. Trial supports PDF, DOC, DOCX, PPT, PPTX, and TXT files only.'
    }
  }

  return { isValid: true }
}

export function TrialUploadZone({
  onFileSelect,
  onFileRemove,
  selectedFile,
  disabled = false,
  className,
  trialsRemaining = 3,
  canUseTrial = true
}: TrialFileUploadProps) {
  const { t } = useTranslation('trial')
  const [dragActive, setDragActive] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Prevent default drag behaviors
  useEffect(() => {
    const preventDefault = (e: DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
    }

    document.addEventListener('dragover', preventDefault)
    document.addEventListener('drop', preventDefault)

    return () => {
      document.removeEventListener('dragover', preventDefault)
      document.removeEventListener('drop', preventDefault)
    }
  }, [])

  const handleFileValidation = useCallback((file: File) => {
    setError(null)
    
    const validation = validateTrialFile(file)
    if (!validation.isValid) {
      setError(validation.error || 'Invalid file')
      return false
    }
    
    return true
  }, [])

  const handleFileSelect = useCallback((file: File) => {
    if (handleFileValidation(file)) {
      onFileSelect(file)
    }
  }, [handleFileValidation, onFileSelect])

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (disabled) return

    const files = e.dataTransfer.files
    if (files && files[0]) {
      handleFileSelect(files[0])
    }
  }, [disabled, handleFileSelect])

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files[0]) {
      handleFileSelect(files[0])
    }
  }, [handleFileSelect])

  const handleRemoveFile = useCallback(() => {
    setError(null)
    onFileRemove?.()
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [onFileRemove])

  const openFileDialog = useCallback(() => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }, [disabled])

  return (
    <div className={cn('w-full', className)}>
      {/* Large Upload Area for Trial */}
      <div
        className={cn(
          'relative border-2 border-dashed rounded-xl p-12 transition-colors cursor-pointer',
          dragActive
            ? 'border-purple-400 bg-purple-50'
            : selectedFile
            ? 'border-green-400 bg-green-50'
            : error
            ? 'border-red-400 bg-red-50'
            : 'border-gray-300 hover:border-purple-400 hover:bg-purple-50',
          disabled && 'opacity-50 cursor-not-allowed'
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          accept={TRIAL_SUPPORTED_TYPES.join(',')}
          onChange={handleInputChange}
          disabled={disabled}
        />

        {selectedFile ? (
          /* File Selected State */
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center">
                <FileText className="w-8 h-8 text-green-600" />
              </div>
              <div>
                <p className="text-xl font-semibold text-gray-900">{selectedFile.name}</p>
                <p className="text-lg text-gray-500">{formatFileSize(selectedFile.size)}</p>
                <p className="text-sm text-green-600 font-medium">{t('upload.readyForAnalysis')}</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="lg"
              onClick={handleRemoveFile}
              disabled={disabled}
              className="text-gray-400 hover:text-red-500"
            >
              <X className="w-6 h-6" />
            </Button>
          </div>
        ) : (
          /* Upload State */
          <div className="text-center">
            <div className="w-20 h-20 mx-auto mb-6 bg-purple-100 rounded-xl flex items-center justify-center">
              <Upload className={cn(
                'w-10 h-10',
                dragActive ? 'text-purple-600' : 'text-purple-500'
              )} />
            </div>
            <div className="space-y-3">
              <h3 className="text-2xl font-bold text-gray-900">
                {dragActive ? t('upload.titleDrop') : t('upload.title')}
              </h3>
              <p className="text-lg text-gray-600">
                {dragActive
                  ? t('upload.descriptionDrop')
                  : t('upload.description')
                }
              </p>
              <div className="text-sm text-gray-500 space-y-1">
                <p>{t('upload.supportedFormats')}</p>
                <p>{t('upload.maxFileSize')}</p>
                <p>{t('upload.freeTrials')}</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="mt-4 flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg">
          <AlertCircle className="w-5 h-5" />
          <span className="text-sm font-medium">{error}</span>
        </div>
      )}

      {/* Trial Info */}
      <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        {canUseTrial ? (
          <p className="text-sm text-yellow-800">
            {t('upload.specialOffer')}
          </p>
        ) : (
          <p className="text-sm text-yellow-800">
            {t('upload.trialsExhausted')}
          </p>
        )}
      </div>
    </div>
  )
}
