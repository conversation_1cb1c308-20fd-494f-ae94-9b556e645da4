import { SignInForm } from '@/components/auth/signin-form'
import { ProtectedRoute } from '@/components/auth/protected-route'

export default function SignInPage() {
  return (
    <ProtectedRoute requireAuth={false}>
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 via-blue-50 to-pink-50">
        <div className="w-full max-w-md p-8">
          <SignInForm />
        </div>
      </div>
    </ProtectedRoute>
  )
}
