import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { getUserById, updateUserCredits, createTransaction, recordUsage } from '@/lib/db'
import { debugRenewUserCredits } from '@/lib/credit-renewal'
import { env } from '@/lib/config'

export async function POST(request: NextRequest) {
  try {
    // Only allow when debug tools are enabled
    if (!env.ENABLE_DEBUG_TOOLS) {
      return NextResponse.json({ error: 'Debug tools not available' }, { status: 403 })
    }

    const session = await getServerSession(authOptions)
    const userId = (session?.user as any)?.id
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { action, amount } = await request.json()

    const user = await getUserById(userId)
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    switch (action) {
      case 'deduct':
        if (!amount || amount <= 0) {
          return NextResponse.json({ error: 'Invalid amount' }, { status: 400 })
        }

        const newCredits = Math.max(0, user.credits - amount)
        await updateUserCredits(user.id, newCredits)

        // Record usage in UsageRecord (for debug deductions)
        await recordUsage({
          userId: user.id,
          operationType: 'debug_deduction',
          creditSpent: amount,
          status: 'completed',
          metadata: {
            type: 'debug',
            action: 'deduct'
          }
        })

        // Create transaction record for debugging (only for debug purposes)
        await createTransaction({
          userId: user.id,
          type: 'debug_deduction',
          amount: 0,
          currency: 'HKD',
          status: 'completed',
          credits: -amount,
          description: `Debug: Deducted ${amount} credits`,
          metadata: {
            type: 'debug',
            action: 'deduct',
            previousCredits: user.credits,
            newCredits,
          }
        })

        return NextResponse.json({
          success: true,
          message: `Deducted ${amount} credits`,
          previousCredits: user.credits,
          newCredits,
        })

      case 'add':
        if (!amount || amount <= 0) {
          return NextResponse.json({ error: 'Invalid amount' }, { status: 400 })
        }

        const addedCredits = user.credits + amount
        await updateUserCredits(user.id, addedCredits)

        // Create transaction record for debugging
        await createTransaction({
          userId: user.id,
          type: 'debug_addition',
          amount: 0,
          currency: 'HKD',
          status: 'completed',
          credits: amount,
          description: `Debug: Added ${amount} credits`,
          metadata: {
            type: 'debug',
            action: 'add',
            previousCredits: user.credits,
            newCredits: addedCredits,
          }
        })

        return NextResponse.json({
          success: true,
          message: `Added ${amount} credits`,
          previousCredits: user.credits,
          newCredits: addedCredits,
        })

      case 'renew':
        // Use debug renewal function that bypasses all conditions
        const result = await debugRenewUserCredits(user.id)

        if (!result) {
          return NextResponse.json({
            success: false,
            message: 'Failed to renew credits',
          })
        }

        return NextResponse.json({
          success: true,
          message: `Credits renewed successfully! Previous: ${result.previousCredits}, New: ${result.newCredits}`,
          result,
        })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    console.error('Error in debug credits API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
