{"containerApps.deploymentConfigurations": [{"label": "aca-docuchamp-dev", "type": "AcrDockerBuildRequest", "dockerfilePath": "Dockerfile", "srcPath": ".", "envPath": ".env.dev", "resourceGroup": "env-docuchampai", "containerApp": "aca-docuchamp-dev", "containerRegistry": "envdocuchamp45d647"}, {"label": "aca-docuchamp-prod", "type": "AcrDockerBuildRequest", "dockerfilePath": "Dockerfile", "srcPath": ".", "envPath": ".env.prod", "resourceGroup": "env-docuchampai", "containerApp": "aca-docuchamp-prod", "containerRegistry": "envdocuchamp45d647"}]}