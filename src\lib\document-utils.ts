/**
 * Document processing and analysis utilities
 */

export interface DocumentMetadata {
  fileName: string
  fileSize: number
  fileType: string
  pageCount?: number
  wordCount?: number
  processingTime?: string
  extractedAt: string
}

export interface AnalysisParams {
  file: File
  analysisType: string
  sections?: string[]
  language: string
  reportLength: 'short' | 'medium' | 'long'
}

export interface AnalysisResult {
  metadata: DocumentMetadata
  markdownContent: string
  docxUrl?: string
}

/**
 * Extract basic metadata from file
 */
export function extractFileMetadata(file: File): Partial<DocumentMetadata> {
  return {
    fileName: file.name,
    fileSize: file.size,
    fileType: file.type,
    extractedAt: new Date().toISOString()
  }
}

/**
 * Estimate word count from text content
 */
export function estimateWordCount(text: string): number {
  return text.trim().split(/\s+/).filter(word => word.length > 0).length
}

/**
 * Estimate page count based on content length
 */
export function estimatePageCount(text: string, wordsPerPage: number = 250): number {
  const wordCount = estimateWordCount(text)
  return Math.max(1, Math.ceil(wordCount / wordsPerPage))
}

/**
 * Generate analysis prompt for AI processing
 */
export function buildAnalysisPrompt(
  text: string,
  analysisType: string,
  sections: string[],
  language: string,
  reportLength: string
): string {
  const lengthInstructions = {
    short: 'Provide a concise summary (approximately 500 words)',
    medium: 'Provide a balanced analysis (approximately 1000 words)',
    long: 'Provide a comprehensive analysis (approximately 2000 words)'
  }

  const sectionsText = sections.length > 0 
    ? `Focus on these specific sections: ${sections.join(', ')}`
    : 'Cover all relevant aspects of the document'

  return `
Analyze the following document with these parameters:
- Analysis Type: ${analysisType}
- Language: ${language}
- Report Length: ${reportLength}
- ${sectionsText}

Instructions:
- ${lengthInstructions[reportLength as keyof typeof lengthInstructions]}
- Use markdown formatting for the output
- Include clear headings and subheadings
- Provide actionable insights where applicable
- Maintain professional tone throughout

Document Content:
${text}

Please provide your analysis in markdown format.
  `.trim()
}

/**
 * Generate mock analysis content for development
 */
export function generateMockAnalysis(params: AnalysisParams): string {
  const { analysisType, sections, language, reportLength } = params
  
  const currentDate = new Date().toLocaleDateString()
  const sectionsText = sections && sections.length > 0 ? sections.join(', ') : 'General Analysis'
  
  return `# Document Analysis Report

**Analysis Type:** ${analysisType}  
**Language:** ${language}  
**Report Length:** ${reportLength}  
**Date:** ${currentDate}  
**Sections Analyzed:** ${sectionsText}

## Executive Summary

This document has been analyzed using AI-powered document analysis technology. The analysis focuses on extracting key insights, identifying important patterns, and providing actionable recommendations based on the document content.

## Key Findings

### 📊 Document Structure
- **File Type:** ${params.file.type}
- **File Size:** ${(params.file.size / 1024 / 1024).toFixed(2)} MB
- **Processing Status:** Completed Successfully

### 🔍 Content Analysis
Based on the ${analysisType} analysis type, the following key points have been identified:

1. **Primary Content Areas**
   - Main topics and themes identified
   - Key data points extracted
   - Important sections highlighted

2. **Quality Assessment**
   - Document completeness: High
   - Information clarity: Good
   - Data consistency: Verified

### 📈 Insights & Recommendations

#### Immediate Actions
- Review highlighted sections for accuracy
- Consider additional documentation where gaps identified
- Implement suggested improvements

#### Long-term Considerations
- Regular review and updates recommended
- Integration with existing workflows
- Monitoring and tracking of key metrics

## Detailed Analysis

${sections ? sections.map(section => `
### ${section}
This section contains detailed analysis specific to ${section.toLowerCase()}. The AI has identified key patterns, extracted relevant data points, and provided contextual insights that can inform decision-making processes.

**Key Points:**
- Comprehensive data extraction completed
- Pattern recognition algorithms applied
- Contextual analysis performed
- Recommendations generated

`).join('') : `
### General Analysis
This section contains comprehensive analysis of the document. The AI has identified key patterns, extracted relevant data points, and provided contextual insights that can inform decision-making processes.

**Key Points:**
- Comprehensive data extraction completed
- Pattern recognition algorithms applied
- Contextual analysis performed
- Recommendations generated

`}

## Technical Details

- **Processing Time:** ~2.5 seconds
- **Analysis Engine:** DocuChamp.ai Advanced
- **Confidence Score:** 95%
- **Language Detection:** ${language}

## Conclusion

The document analysis has been completed successfully. All requested sections have been processed and analyzed according to the specified parameters. The insights provided should serve as a foundation for informed decision-making.

---

*This analysis was generated by DocuChamp.ai - Advanced Document Analysis Platform*  
*Report ID: ${Date.now()}*  
*Generated on: ${new Date().toLocaleString()}*
`
}

/**
 * Validate analysis parameters
 */
export function validateAnalysisParams(params: Partial<AnalysisParams>): { isValid: boolean; error?: string } {
  if (!params.file) {
    return { isValid: false, error: 'File is required' }
  }
  
  if (!params.analysisType) {
    return { isValid: false, error: 'Analysis type is required' }
  }
  
  if (!params.language) {
    return { isValid: false, error: 'Language is required' }
  }
  
  if (!params.reportLength || !['short', 'medium', 'long'].includes(params.reportLength)) {
    return { isValid: false, error: 'Valid report length is required (short, medium, or long)' }
  }
  
  return { isValid: true }
}

/**
 * Create form data for analysis request
 */
export function createAnalysisFormData(params: AnalysisParams): FormData {
  const formData = new FormData()
  formData.append('file', params.file)
  formData.append('analysisType', params.analysisType)

  // Only include sections if provided
  if (params.sections) {
    formData.append('sections', JSON.stringify(params.sections))
  }

  formData.append('language', params.language)
  formData.append('reportLength', params.reportLength)

  return formData
}
