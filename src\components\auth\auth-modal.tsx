'use client'

import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { X } from 'lucide-react'
import { SignInForm } from './signin-form'
import { SignUpForm } from './signup-form'
import { useTranslation } from '@/hooks/use-translation'

interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
  initialMode?: 'signin' | 'signup'
}

export function AuthModal({ isOpen, onClose, initialMode = 'signin' }: AuthModalProps) {
  const [mode, setMode] = useState<'signin' | 'signup'>(initialMode)
  const [mounted, setMounted] = useState(false)
  const { t } = useTranslation('common')


  // Ensure component is mounted before rendering portal
  useEffect(() => {
    setMounted(true)
  }, [])



  // Reset mode when modal opens
  useEffect(() => {
    if (isOpen) {
      setMode(initialMode)
    }
  }, [isOpen, initialMode])

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  // Close modal on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  if (!isOpen || !mounted) return null

  const modalContent = (
    <div className="auth-modal-backdrop flex items-center justify-center">
      {/* Backdrop with blur - no click to close */}
      <div className="absolute inset-0" />

      {/* Modal Content */}
      <div className="auth-modal-content w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors z-10"
        >
          <X className="w-5 h-5" />
        </button>

        {/* Modal Body */}
        <div className="p-8">
          {mode === 'signin' ? (
            <div>
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-gray-900">{t('auth.signInTitle')}</h2>
                <p className="text-gray-600 mt-2">
                  {t('auth.noAccount')}{' '}
                  <button
                    onClick={() => setMode('signup')}
                    className="text-purple-600 hover:text-purple-700 font-medium hover:underline cursor-pointer transition-all duration-200"
                  >
                    {t('auth.joinHere')}
                  </button>
                </p>
              </div>
              <SignInForm onSuccess={onClose} />
            </div>
          ) : (
            <div>
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-gray-900">{t('auth.signUpTitle')}</h2>
                <p className="text-gray-600 mt-2">
                  {t('auth.hasAccount')}{' '}
                  <button
                    onClick={() => setMode('signin')}
                    className="text-purple-600 hover:text-purple-700 font-medium hover:underline cursor-pointer transition-all duration-200"
                  >
                    {t('auth.signInHere')}
                  </button>
                </p>
              </div>
              <SignUpForm onSuccess={onClose} />
            </div>
          )}
        </div>
      </div>
    </div>
  )

  return createPortal(modalContent, document.body)
}
