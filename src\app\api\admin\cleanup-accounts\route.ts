import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    // Simple authentication - check for admin secret
    const { secret } = await request.json()

    if (secret !== process.env.ADMIN_SECRET) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // DISABLED: Account cleanup is disabled to prevent accidental deletion

    return NextResponse.json({
      message: 'Account cleanup is disabled to prevent accidental deletion of user accounts',
      deletedCount: 0,
      status: 'disabled'
    })
  } catch (error) {
    console.error('Cleanup error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
