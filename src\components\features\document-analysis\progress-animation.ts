/**
 * Progress Animation Utilities for Document Analysis
 * 
 * This module handles the smooth progress bar animations and timing logic
 * for document analysis operations.
 */

export interface ProgressAnimationConfig {
  currentProgress: number;
  targetProgress: number;
  animationDuration?: number;
  steps?: number;
  onProgress: (progress: number) => void;
  onComplete: () => void;
}

/**
 * Calculate estimated processing time based on file size
 */
export const calculateEstimatedTime = (fileSizeBytes: number): number => {
  // More conservative timing: Base time: 15 seconds for small files
  // Additional time: 8 seconds per MB
  const fileSizeMB = fileSizeBytes / (1024 * 1024);
  const baseTime = 15; // 15 seconds base
  const additionalTime = Math.max(0, fileSizeMB - 1) * 8; // 8 seconds per MB after first MB
  return Math.min(baseTime + additionalTime, 180); // Cap at 3 minutes
};

/**
 * Format estimated time for display
 */
export const formatEstimatedTime = (seconds: number): string => {
  if (seconds < 60) {
    return `${Math.round(seconds)} seconds`;
  } else if (seconds < 120) {
    return `${Math.round(seconds / 60)} minute`;
  } else {
    return `${Math.round(seconds / 60)} minutes`;
  }
};

/**
 * Create a smooth progress animation from current to target progress
 */
export const createProgressAnimation = (config: ProgressAnimationConfig): (() => void) => {
  const {
    currentProgress,
    targetProgress,
    animationDuration = 1000,
    steps = 20,
    onProgress,
    onComplete
  } = config;

  const stepDuration = animationDuration / steps;
  const progressIncrement = (targetProgress - currentProgress) / steps;

  let step = 0;
  const progressAnimation = setInterval(() => {
    step++;
    const newProgress = Math.min(currentProgress + (progressIncrement * step), targetProgress);
    onProgress(newProgress);

    if (step >= steps || newProgress >= targetProgress) {
      clearInterval(progressAnimation);
      onProgress(targetProgress);
      
      // Wait a brief moment at target progress before completing
      setTimeout(() => {
        onComplete();
      }, 300);
    }
  }, stepDuration);

  // Return cleanup function
  return () => clearInterval(progressAnimation);
};

/**
 * Start a file-size-based progress timer
 */
export const createProgressTimer = (
  fileSizeBytes: number,
  onProgress: (progress: number) => void,
  onTimeout: () => void
): { timer: NodeJS.Timeout; cleanup: () => void } => {
  const estimatedSeconds = calculateEstimatedTime(fileSizeBytes);
  const updateInterval = 500; // Update every 0.5 seconds for smoother progress

  // Define step durations - more aggressive timing
  const step1Duration = Math.max(2, estimatedSeconds * 0.20);  // 20% of time for text extraction (min 2s)
  const step2Duration = Math.max(3, estimatedSeconds * 0.30);  // 30% of time for structure analysis (min 3s)
  const step3Duration = Math.max(3, estimatedSeconds * 0.30);  // 30% of time for insights (min 3s)
  const step4Duration = Math.max(2, estimatedSeconds * 0.20);  // 20% of time for finalizing (min 2s)

  const stepThresholds = [
    step1Duration,                                    // Step 2: Extracting document content (25%)
    step1Duration + step2Duration,                    // Step 3: Analyzing document structure (50%)
    step1Duration + step2Duration + step3Duration,   // Step 4: Generating insights (75%)
    step1Duration + step2Duration + step3Duration + step4Duration  // Step 5: Finalizing report (99%)
  ];

  let currentProgress = 0;
  let elapsedSeconds = 0;

  const timer = setInterval(() => {
    elapsedSeconds += 0.5;

    // Calculate progress based on which step we're in
    if (elapsedSeconds <= stepThresholds[0]) {
      // Step 2: Extracting document content (0% to 25%)
      currentProgress = (elapsedSeconds / stepThresholds[0]) * 25;
    } else if (elapsedSeconds <= stepThresholds[1]) {
      // Step 3: Analyzing document structure (25% to 50%)
      const stepProgress = (elapsedSeconds - stepThresholds[0]) / step2Duration;
      currentProgress = 25 + (stepProgress * 25);
    } else if (elapsedSeconds <= stepThresholds[2]) {
      // Step 4: Generating insights (50% to 75%)
      const stepProgress = (elapsedSeconds - stepThresholds[1]) / step3Duration;
      currentProgress = 50 + (stepProgress * 25);
    } else if (elapsedSeconds <= stepThresholds[3]) {
      // Step 5: Finalizing report (75% to 99%)
      const stepProgress = (elapsedSeconds - stepThresholds[2]) / step4Duration;
      currentProgress = 75 + (stepProgress * 24); // Cap at 99%
    } else {
      // Cap at 99% until webhook completes
      currentProgress = 99;
      clearInterval(timer);
    }

    onProgress(Math.min(currentProgress, 99));
  }, updateInterval);

  // Dynamic timeout: 3x estimated time, maximum 5 minutes
  const timeoutSeconds = Math.min(estimatedSeconds * 3, 300); // 3x estimated time, max 5 minutes
  const timeoutMs = timeoutSeconds * 1000;

  const timeoutTimer = setTimeout(() => {
    console.log('⏰ Progress timer timeout reached, checking operation status');
    clearInterval(timer);
    onTimeout();
  }, timeoutMs);

  const cleanup = () => {
    clearInterval(timer);
    clearTimeout(timeoutTimer);
  };

  return { timer, cleanup };
};
