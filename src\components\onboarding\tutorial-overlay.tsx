'use client'

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { X, ChevronLeft, ChevronRight, Info } from 'lucide-react'

export interface TutorialStep {
  id: string
  title: string
  content: string
  targetSelector: string
  position: 'top' | 'bottom' | 'left' | 'right' | 'center'
  highlightPadding?: number
}

interface TutorialOverlayProps {
  isOpen: boolean
  onClose: () => void
  steps: TutorialStep[]
  onComplete?: () => void
}

export function TutorialOverlay({ isOpen, onClose, steps, onComplete }: TutorialOverlayProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null)
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 })

  useEffect(() => {
    if (!isOpen || !steps[currentStep]) return

    const findAndHighlightElement = () => {
      const element = document.querySelector(steps[currentStep].targetSelector) as HTMLElement
      if (element) {
        setTargetElement(element)
        calculateTooltipPosition(element)

        // Scroll element into view
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'center'
        })
      }
    }

    // Small delay to ensure DOM is ready
    const timer = setTimeout(findAndHighlightElement, 100)

    // Add scroll listener to update positions when page scrolls
    const handleScroll = () => {
      if (targetElement) {
        calculateTooltipPosition(targetElement)
      }
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    window.addEventListener('resize', handleScroll, { passive: true })

    return () => {
      clearTimeout(timer)
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', handleScroll)
    }
  }, [currentStep, isOpen, steps, targetElement])

  const calculateTooltipPosition = (element: HTMLElement) => {
    const rect = element.getBoundingClientRect()
    const step = steps[currentStep]
    const tooltipWidth = 384 // max-w-sm = 24rem = 384px
    const tooltipHeight = 300 // estimated height
    const margin = 20

    let top = 0
    let left = 0

    switch (step.position) {
      case 'top':
        top = rect.top - tooltipHeight - margin
        left = rect.left + rect.width / 2
        // Ensure tooltip stays within viewport
        if (top < margin) {
          top = rect.bottom + margin // Flip to bottom if no space above
        }
        break
      case 'bottom':
        top = rect.bottom + margin
        left = rect.left + rect.width / 2
        // Ensure tooltip stays within viewport
        if (top + tooltipHeight > window.innerHeight - margin) {
          top = rect.top - tooltipHeight - margin // Flip to top if no space below
        }
        break
      case 'left':
        top = rect.top + rect.height / 2
        left = rect.left - tooltipWidth - margin
        // Ensure tooltip stays within viewport
        if (left < margin) {
          left = rect.right + margin // Flip to right if no space left
        }
        break
      case 'right':
        top = rect.top + rect.height / 2
        left = rect.right + margin
        // Ensure tooltip stays within viewport
        if (left + tooltipWidth > window.innerWidth - margin) {
          left = rect.left - tooltipWidth - margin // Flip to left if no space right
        }
        break
      case 'center':
        top = window.innerHeight / 2
        left = window.innerWidth / 2
        break
    }

    // Final boundary checks to ensure tooltip stays within viewport
    if (step.position !== 'center') {
      // Horizontal bounds
      if (left - tooltipWidth / 2 < margin) {
        left = margin + tooltipWidth / 2
      } else if (left + tooltipWidth / 2 > window.innerWidth - margin) {
        left = window.innerWidth - margin - tooltipWidth / 2
      }

      // Vertical bounds
      if (top < margin) {
        top = margin
      } else if (top + tooltipHeight > window.innerHeight - margin) {
        top = window.innerHeight - margin - tooltipHeight
      }
    }

    setTooltipPosition({ top, left })
  }

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      handleComplete()
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleComplete = () => {
    onComplete?.()
    onClose()
  }

  const handleSkip = () => {
    onClose()
  }

  // Reset to first step when tutorial is closed and reopened
  useEffect(() => {
    if (!isOpen) {
      setCurrentStep(0)
      setTargetElement(null)
      setTooltipPosition({ top: 0, left: 0 })
    }
  }, [isOpen])

  if (!isOpen || !steps[currentStep]) return null

  const currentStepData = steps[currentStep]
  const isLastStep = currentStep === steps.length - 1

  return (
    <div className="fixed inset-0 z-50" style={{ pointerEvents: 'none' }}>
      {/* Blurred backdrop with cutout for highlighted element */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Create multiple divs to create a "cutout" effect */}
        {targetElement && currentStepData.position !== 'center' ? (
          <>
            {/* Top section */}
            <div
              className="absolute"
              style={{
                top: 0,
                left: 0,
                right: 0,
                height: Math.max(0, targetElement.getBoundingClientRect().top - (currentStepData.highlightPadding || 8)),
                background: 'rgba(0, 0, 0, 0.3)',
                backdropFilter: 'blur(4px)'
              }}
            />
            {/* Bottom section */}
            <div
              className="absolute"
              style={{
                top: targetElement.getBoundingClientRect().bottom + (currentStepData.highlightPadding || 8),
                left: 0,
                right: 0,
                bottom: 0,
                background: 'rgba(0, 0, 0, 0.3)',
                backdropFilter: 'blur(4px)'
              }}
            />
            {/* Left section */}
            <div
              className="absolute"
              style={{
                top: targetElement.getBoundingClientRect().top - (currentStepData.highlightPadding || 8),
                left: 0,
                width: Math.max(0, targetElement.getBoundingClientRect().left - (currentStepData.highlightPadding || 8)),
                height: targetElement.getBoundingClientRect().height + 2 * (currentStepData.highlightPadding || 8),
                background: 'rgba(0, 0, 0, 0.3)',
                backdropFilter: 'blur(4px)'
              }}
            />
            {/* Right section */}
            <div
              className="absolute"
              style={{
                top: targetElement.getBoundingClientRect().top - (currentStepData.highlightPadding || 8),
                left: targetElement.getBoundingClientRect().right + (currentStepData.highlightPadding || 8),
                right: 0,
                height: targetElement.getBoundingClientRect().height + 2 * (currentStepData.highlightPadding || 8),
                background: 'rgba(0, 0, 0, 0.3)',
                backdropFilter: 'blur(4px)'
              }}
            />
            {/* Highlight border around target element - using box-shadow for clean transparent corners */}
            <div
              className="absolute pointer-events-none"
              style={{
                top: targetElement.getBoundingClientRect().top - (currentStepData.highlightPadding || 8),
                left: targetElement.getBoundingClientRect().left - (currentStepData.highlightPadding || 8),
                width: targetElement.getBoundingClientRect().width + 2 * (currentStepData.highlightPadding || 8),
                height: targetElement.getBoundingClientRect().height + 2 * (currentStepData.highlightPadding || 8),
                borderRadius: '8px',
                background: 'transparent',
                boxShadow: '0 0 0 4px rgba(147, 51, 234, 0.8)'
              }}
            />
          </>
        ) : (
          /* Full blur for center-positioned steps or when no target element */
          <div
            className="absolute inset-0 pointer-events-none"
            style={{
              background: 'rgba(0, 0, 0, 0.3)',
              backdropFilter: 'blur(4px)'
            }}
          />
        )}
      </div>

      {/* Tutorial tooltip */}
      <div
        className="bg-white rounded-lg shadow-xl border border-gray-200 p-6 max-w-sm z-50"
        style={{
          position: currentStepData.position === 'center' ? 'fixed' : 'absolute',
          top: currentStepData.position === 'center' ? '50%' : tooltipPosition.top,
          left: currentStepData.position === 'center' ? '50%' : tooltipPosition.left,
          transform: currentStepData.position === 'center'
            ? 'translate(-50%, -50%)'
            : currentStepData.position === 'top' || currentStepData.position === 'bottom'
              ? 'translateX(-50%)'
              : currentStepData.position === 'left'
                ? 'translateX(-100%)'
                : 'translateX(0)',
          maxHeight: '80vh',
          overflow: 'auto',
          pointerEvents: 'auto'
        }}
      >
        {/* Close button */}
        <button
          onClick={handleSkip}
          className="absolute top-2 right-2 p-1 text-gray-400 hover:text-gray-600"
        >
          <X className="w-4 h-4" />
        </button>

        {/* Step indicator */}
        <div className="text-sm text-purple-600 font-medium mb-2">
          Step {currentStep + 1} of {steps.length}
        </div>

        {/* Title */}
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          {currentStepData.title}
        </h3>

        {/* Content */}
        <p className="text-gray-600 mb-6">
          {currentStepData.content}
        </p>

        {/* Navigation buttons */}
        <div className="flex justify-between items-center">
          <div className="flex space-x-2">
            {currentStep > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrevious}
                className="flex items-center"
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
                Previous
              </Button>
            )}
          </div>

          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSkip}
            >
              Skip Tutorial
            </Button>
            <Button
              size="sm"
              onClick={handleNext}
              className="bg-purple-600 hover:bg-purple-700 text-white flex items-center"
            >
              {isLastStep ? 'Complete' : 'Next'}
              {!isLastStep && <ChevronRight className="w-4 h-4 ml-1" />}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

// Tutorial trigger button component
interface TutorialTriggerProps {
  onClick: () => void
  className?: string
}

export function TutorialTrigger({ onClick, className = '' }: TutorialTriggerProps) {
  return (
    <button
      onClick={onClick}
      className={`inline-flex items-center px-2 py-1 text-sm text-purple-600 hover:text-purple-700 hover:bg-purple-50 rounded-md transition-colors ${className}`}
      title="Start Tutorial"
    >
      <Info className="w-4 h-4" />
    </button>
  )
}
