import type { Metadata } from 'next'
import { generateMetadata } from '@/lib/seo-config'

export const metadata: Metadata = generateMetadata({
  title: 'Document Analysis',
  description: 'Advanced AI document analysis with customizable templates. Analyze garment BOMs, legal documents, financial reports, and more. Extract insights from text, images, and tables.',
  keywords: [
    'DocuChampAI',
    'document analysis tool',
    'AI document processing',
    'garment BOM analysis',
    'legal document validation',
    'financial report analysis',
    'education content validation',
    'custom document templates',
    'multimodal AI analysis',
    'business document automation',
    'intelligent document processing',
    'document champion'
  ],
  image: '/document-analysis-preview.webp',
})

export default function DocumentAnalysisLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
