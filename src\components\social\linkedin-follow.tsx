'use client'

import { Linkedin } from 'lucide-react'

interface LinkedInFollowProps {
  companyUrl?: string
  className?: string
}

export function LinkedInFollow({
  companyUrl = 'https://www.linkedin.com/company/docuchampai',
  className = ''
}: LinkedInFollowProps) {
  const handleFollow = () => {
    window.open(companyUrl, '_blank', 'noopener,noreferrer')
  }

  return (
    <div className={className}>
      <button
        onClick={handleFollow}
        className="group relative w-full max-w-xs overflow-hidden rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 px-4 py-2 text-white shadow-md transition-all duration-300 hover:from-blue-700 hover:to-blue-800 hover:shadow-lg hover:scale-105 hover:-translate-y-0.5 active:scale-95 cursor-pointer"
        title="Follow us on LinkedIn"
      >
        <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        <div className="relative flex items-center justify-center space-x-2">
          <Linkedin className="w-4 h-4" />
          <span className="font-medium text-sm">Follow on LinkedIn</span>
        </div>
      </button>
    </div>
  )
}

// Note: LinkedIn doesn't provide official embed widgets for company follows
// This is a custom button that opens LinkedIn in a new tab
// LinkedIn does have a "Follow Company" plugin but it requires specific setup and approval
