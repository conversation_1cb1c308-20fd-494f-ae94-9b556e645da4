generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId], map: "accounts_userId_fkey")
  @@map("accounts")
}

model User {
  id                 String                     @id @default(cuid())
  name               String?
  email              String                     @unique
  emailVerified      DateTime?
  image              String?
  password           String?
  credits            Int                        @default(0)
  createdAt          DateTime                   @default(now())
  updatedAt          DateTime                   @updatedAt
  creditsRenewAt     DateTime?
  lastCreditRenewal  DateTime?
  monthlyCredits     Int                        @default(0)
  stripeCustomerId   String?                    @unique
  accounts           Account[]
  analysisTemplates  DocumentAnalysisTemplate[]
  subscriptions      Subscription[]
  transactions       Transaction[]
  usageRecords       UsageRecord[]
  verificationTokens VerificationToken[]

  @@map("users")
}

model VerificationToken {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  type      String
  expiresAt DateTime
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "verification_tokens_userId_fkey")
  @@map("verification_tokens")
}

model Subscription {
  id                   String    @id @default(cuid())
  userId               String
  stripeSubscriptionId String?   @unique
  stripeCustomerId     String?
  status               String
  currentPeriodStart   DateTime?
  currentPeriodEnd     DateTime?
  cancelAtPeriodEnd    Boolean   @default(false)
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt
  productName          String?
  user                 User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "subscriptions_userId_fkey")
  @@map("subscriptions")
}

model Transaction {
  id                    String   @id @default(cuid())
  userId                String
  type                  String
  amount                Int
  currency              String   @default("HKD")
  status                String
  stripePaymentIntentId String?  @unique
  stripeInvoiceId       String?
  credits               Int?
  description           String?
  metadata              Json?
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  billingReason         String?
  cancelAtPeriodEnd     Boolean?
  canceledAt            String?
  creditDifference      Int?
  maxCreditsInPeriod    Int?
  maxPackageInPeriod    String?
  nextRenewalDate       String?
  packageCredit         Int?
  packageName           String?
  periodEnd             String?
  previousCredits       Int?
  previousPackage       String?
  previousPackageCredit Int?
  reason                String?
  renewalDate           String?
  stripeSessionId       String?
  stripeSubscriptionId  String?
  subscriptionCredits   Int?
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "transactions_userId_fkey")
  @@map("transactions")
}

model UsageRecord {
  id            String    @id @default(cuid())
  userId        String
  operationId   String?   @unique
  operationType String    @default("document_analysis")
  creditSpent   Int       @default(1)
  status        String    @default("pending")
  fileLink      String?   @db.Text
  fileName      String?
  fileSize      Int?
  metadata      Json?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  completedAt   DateTime?
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([operationId])
  @@index([status])
  @@map("usage_records")
}

model SupportTicket {
  id        String   @id @default(cuid())
  caseId    String   @unique
  name      String
  email     String
  phone     String?
  subject   String
  message   String   @db.Text
  status    String   @default("open")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("support_tickets")
}

model TrialUsage {
  id          String   @id @default(cuid())
  userId      String   // Anonymous user ID from client
  fingerprint String   // Device fingerprint for tracking
  userAgent   String?  // Browser user agent (for logging only)
  timestamp   DateTime @default(now())
  createdAt   DateTime @default(now())

  @@index([fingerprint])
  @@index([createdAt])
  @@map("trial_usage")
}

model ProcessedEvent {
  id            String    @id @default(cuid())
  stripeEventId String    @unique
  eventType     String
  processed     Boolean   @default(false)
  processedAt   DateTime?
  createdAt     DateTime  @default(now())
  metadata      Json?

  @@map("processed_events")
}

model DocumentAnalysisTemplate {
  id         String   @id @default(cuid())
  userId     String
  useCaseId  String   @db.VarChar(50)
  name       String   @db.VarChar(100)
  sections   Json
  isPublic   Boolean  @default(false)
  usageCount Int      @default(0)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, useCaseId, name])
  @@index([userId, useCaseId])
  @@index([isPublic, useCaseId])
  @@map("document_analysis_templates")
}
