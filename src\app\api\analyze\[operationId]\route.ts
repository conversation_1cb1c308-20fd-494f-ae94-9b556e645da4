import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { getUsageRecordByOperationId } from '@/lib/db'

// GET /api/analyze/[operationId]
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ operationId: string }> }
) {
  try {
    const { operationId } = await params

    // Check if this is a trial operation
    const isTrial = operationId.startsWith('trial_')

    if (isTrial) {
      // For trial operations, we don't have usage records in the database
      // Return a basic response indicating it's a trial operation
      return NextResponse.json({
        operationId,
        success: false, // We don't know the status without checking the SSE stream
        operationType: 'document_analysis',
        creditSpent: 0,
        fileName: null,
        fileSize: null,
        output_file_blob_sas_url: null,
        createdAt: null,
        updatedAt: null,
        completedAt: null,
        metadata: { isTrial: true },
        markdownContent: null,
        isTrial: true
      })
    }

    // For non-trial operations, require authentication
    const session = await getServerSession(authOptions)
    const userId = (session?.user as any)?.id

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the usage record
    const usageRecord = await getUsageRecordByOperationId(operationId)

    if (!usageRecord) {
      return NextResponse.json({ error: 'Operation not found' }, { status: 404 })
    }

    // Verify the operation belongs to the current user
    if (usageRecord.userId !== userId) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Return operation status
    return NextResponse.json({
      operationId: usageRecord.operationId,
      success: usageRecord.status === 'completed',
      operationType: usageRecord.operationType,
      creditSpent: usageRecord.creditSpent,
      fileName: usageRecord.fileName,
      fileSize: usageRecord.fileSize,
      output_file_blob_sas_url: usageRecord.fileLink,
      createdAt: usageRecord.createdAt,
      updatedAt: usageRecord.updatedAt,
      completedAt: usageRecord.completedAt,
      metadata: usageRecord.metadata,
      markdownContent: (usageRecord.metadata as any)?.markdownContent || null
    })

  } catch (error: any) {
    console.error('Error getting operation status:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 })
  }
}
