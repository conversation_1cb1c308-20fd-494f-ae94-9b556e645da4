import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const { userId, fingerprint, timestamp } = await request.json()

    if (!userId || !fingerprint || !timestamp) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Get additional headers for enhanced tracking (not for limiting)
    const userAgent = request.headers.get('user-agent') || 'unknown'
    const acceptLanguage = request.headers.get('accept-language') || 'unknown'
    const acceptEncoding = request.headers.get('accept-encoding') || 'unknown'

    // Check for potential abuse patterns before recording
    const now = new Date()
    const recentAttempts = await prisma.trialUsage.count({
      where: {
        fingerprint: fingerprint,
        createdAt: {
          gte: new Date(now.getTime() - (60 * 1000)) // Last 1 minute
        }
      }
    })

    // Prevent rapid-fire attempts (more than 1 per minute)
    if (recentAttempts >= 1) {
      return NextResponse.json({
        error: 'Rate limit exceeded',
        message: 'Please wait before making another attempt'
      }, { status: 429 })
    }

    // Record the trial usage with enhanced tracking data
    await prisma.trialUsage.create({
      data: {
        userId,
        fingerprint,
        timestamp: new Date(timestamp),
        // Store additional metadata in userAgent field as JSON string
        userAgent: JSON.stringify({
          userAgent,
          acceptLanguage,
          acceptEncoding,
          recordedAt: now.toISOString()
        })
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Trial usage recorded successfully'
    })

  } catch (error) {
    console.error('Trial recording error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
