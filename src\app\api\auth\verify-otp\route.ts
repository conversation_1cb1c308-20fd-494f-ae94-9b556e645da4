import { NextRequest, NextResponse } from 'next/server'
import { verifyOTP } from '@/lib/otp'
import { verifyUserEmail, getUserById } from '@/lib/db'
import { generateToken } from '@/lib/auth-utils'

export async function POST(request: NextRequest) {
  try {
    const { userId, otp } = await request.json()

    // Validate input
    if (!userId || !otp) {
      return NextResponse.json(
        { error: 'User ID and OTP are required' },
        { status: 400 }
      )
    }

    // Validate OTP format (6 digits)
    if (!/^\d{6}$/.test(otp)) {
      return NextResponse.json(
        { error: 'OTP must be a 6-digit number' },
        { status: 400 }
      )
    }

    // Check if user exists
    const user = await getUserById(userId)
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if user is already verified
    if (user.emailVerified) {
      return NextResponse.json(
        { error: 'Email is already verified' },
        { status: 400 }
      )
    }

    // Verify OTP
    const verification = await verifyOTP(userId, otp, 'email_verification')
    
    if (!verification.valid) {
      if (verification.expired) {
        return NextResponse.json(
          { 
            error: 'OTP has expired. Please request a new verification code.',
            code: 'OTP_EXPIRED'
          },
          { status: 400 }
        )
      }
      
      return NextResponse.json(
        { 
          error: 'Invalid OTP. Please check your code and try again.',
          code: 'INVALID_OTP'
        },
        { status: 400 }
      )
    }

    // OTP is valid, verify the user's email and give welcome credits
    const verifiedUser = await verifyUserEmail(userId)

    console.log(`✅ Email verified for user: ${verifiedUser.email}`)

    // Generate a temporary sign-in token for automatic login
    const signInToken = generateToken(
      {
        userId: verifiedUser.id,
        email: verifiedUser.email,
        purpose: 'auto_signin_after_verification'
      },
      '5m' // Token expires in 5 minutes
    )

    // Check if this is a new user (just got welcome credits)
    const isNewUser = verifiedUser.credits === 10

    // Return success response with sign-in token
    return NextResponse.json(
      {
        message: 'Email verified successfully! Signing you in...',
        user: {
          id: verifiedUser.id,
          email: verifiedUser.email,
          name: verifiedUser.name,
          credits: verifiedUser.credits,
          emailVerified: verifiedUser.emailVerified,
        },
        signInToken,
        isNewUser
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('❌ OTP verification error:', error)
    return NextResponse.json(
      { error: 'An error occurred during verification. Please try again.' },
      { status: 500 }
    )
  }
}
