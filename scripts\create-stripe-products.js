#!/usr/bin/env node

// Load environment variables
require('dotenv').config({ path: '.env.prod' })

const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY)

async function createStripeProducts() {
  console.log('🚀 Creating Stripe Products and Prices...')
  
  if (!process.env.STRIPE_SECRET_KEY) {
    console.error('❌ STRIPE_SECRET_KEY not found in .env file!')
    process.exit(1)
  }

  try {
    // Define the products and their pricing plans
    const products = [
      {
        name: 'Starter',
        description: 'Perfect for individuals getting started with AI document analysis',
        credits: 300,
        price:18000, // HKD 180.00 in cents
      },
      {
        name: 'Professional',
        description: 'Ideal for professionals with regular analysis needs',
        credits: 600,
        price: 34000, // HKD 340.00 in cents
      },
      {
        name: 'Premium',
        description: 'Best for power users with high-volume analysis requirements',
        credits: 1200,
        price: 68000, // HKD 680.00 in cents
      }
    ]

    const priceIds = {}

    // Create separate products and prices
    for (const productData of products) {
      console.log(`📦 Creating product: ${productData.name}`)

      // Create the product
      const product = await stripe.products.create({
        name: productData.name,
        description: `${productData.description} - ${productData.credits} credits per month`,
        type: 'service',
        metadata: {
          credits: productData.credits.toString(),
          plan_type: 'monthly_credits'
        }
      })

      console.log(`✅ Product created: ${product.id}`)

      // Create the price for this product
      console.log(`💰 Creating price for ${productData.name}...`)

      const price = await stripe.prices.create({
        product: product.id,
        unit_amount: productData.price,
        currency: 'hkd',
        recurring: {
          interval: 'month',
        },
        nickname: `${productData.name} - ${productData.credits} credits/month`,
        metadata: {
          credits: productData.credits.toString(),
          plan_name: productData.name,
          type: 'monthly_credits'
        }
      })

      priceIds[productData.name] = price.id
      console.log(`✅ ${productData.name}: ${price.id}`)
    }

    console.log('\n🎉 Stripe products created successfully!')
    console.log('\n📋 Summary:')
    console.log(`Created ${products.length} separate products with their respective prices:`)
    Object.entries(priceIds).forEach(([name, id]) => {
      console.log(`  ${name}: ${id}`)
    })

    console.log('\n📝 Environment Variables to Add:')
    console.log('Add these to your .env files:')
    console.log('')
    console.log('# Stripe Price IDs - Monthly Subscription Plans')
    Object.entries(priceIds).forEach(([name, id]) => {
      const envVarName = `STRIPE_PRICE_ID_${name.toUpperCase()}`
      console.log(`${envVarName}="${id}"`)
    })

    console.log('\n🔗 Next steps:')
    console.log('1. Copy the environment variables above to your .env files')
    console.log('2. Set up webhook endpoint (for local development, use Stripe CLI)')
    console.log('3. Test payment flow: npm run dev')
    console.log('4. Try subscribing to a plan!')

  } catch (error) {
    console.error('❌ Error creating Stripe products:', error.message)
    process.exit(1)
  }
}

createStripeProducts()
