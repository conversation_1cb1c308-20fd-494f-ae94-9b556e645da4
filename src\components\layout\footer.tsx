'use client'

import Link from 'next/link'
import { getAppName } from '@/lib/config'
import { Linkedin, Facebook, Instagram } from 'lucide-react'
import { FacebookFollow } from '@/components/social/facebook-follow'
import { XFollow } from '@/components/social/twitter-follow'
import { InstagramFollow } from '@/components/social/instagram-follow'
import { LinkedInFollow } from '@/components/social/linkedin-follow'
import { useTranslation } from '@/hooks/use-translation'

export function Footer() {
  const { t } = useTranslation('common')

  return (
    <footer className="bg-gray-50 border-t">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2 lg:col-span-1">
            <div className="flex items-center mb-4">
              <img
                src="/docuchampai.webp"
                alt="DocuChampAI Logo"
                className="h-8"
              />
            </div>
            <p className="text-gray-600 text-sm max-w-md">
              {t('footer.companyDescription')}
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
              {t('footer.sections.quickLinks')}
            </h3>
            <ul className="space-y-2">
              <li>
                <Link href="/pricing" className="text-gray-600 hover:text-gray-900 text-sm" title="View pricing plans and subscription options">
                  {t('footer.links.pricing')}
                </Link>
              </li>

              <li>
                <Link href="/contact" className="text-gray-600 hover:text-gray-900 text-sm" title="Contact our support team">
                  {t('footer.links.contact')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
              {t('footer.sections.legal')}
            </h3>
            <ul className="space-y-2">
              <li>
                <Link href="/privacy" className="text-gray-600 hover:text-gray-900 text-sm" title="Read our privacy policy">
                  {t('footer.links.privacy')}
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-gray-600 hover:text-gray-900 text-sm" title="Read our terms of service">
                  {t('footer.links.terms')}
                </Link>
              </li>
              <li>
                <Link href="/cookies" className="text-gray-600 hover:text-gray-900 text-sm" title="Read our cookie policy">
                  {t('footer.links.cookies')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Social Media */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-6">
              {t('footer.social.followUs')}
            </h3>

            {/* Modern Social Media Buttons */}
            <div className="space-y-4 mb-6 max-w-xs">
              {/* X (Twitter) Follow Button */}
              <XFollow
                size="medium"
                showCount={false}
                className="block"
              />

              {/* Facebook Follow Button */}
              <FacebookFollow
                pageUrl={process.env.NEXT_PUBLIC_FACEBOOK_URL || 'https://www.facebook.com/docuchampai'}
                className="block"
              />

              {/* LinkedIn Follow Button */}
              <LinkedInFollow
                companyUrl={process.env.NEXT_PUBLIC_LINKEDIN_URL || 'https://www.linkedin.com/company/docuchampai'}
                className="block"
              />

              {/* Instagram Follow Button */}
              <InstagramFollow
                username="docuchampai"
                className="block"
              />
            </div>

            {/* Additional Social Links (if needed) */}
            <div className="flex space-x-4 mt-2">
              <span className="text-xs text-gray-500">{t('footer.social.connectMessage')}</span>
            </div>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t border-gray-200">
          <p className="text-center text-gray-400 text-sm">
            © {new Date().getFullYear()} {getAppName()}. {t('footer.copyright')}
          </p>
        </div>
      </div>
    </footer>
  )
}
