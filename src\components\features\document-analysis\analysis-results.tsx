'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { Download, History } from 'lucide-react'
import Link from 'next/link'

interface AnalysisResult {
  metadata?: any;
  markdownContent: string;
  docxUrl?: string;
}

interface AnalysisResultsProps {
  analysisResult: AnalysisResult | null;
  onViewResults: () => void;
}

export function AnalysisResults({ 
  analysisResult, 
  onViewResults 
}: AnalysisResultsProps) {
  if (!analysisResult) {
    return null;
  }

  const handleDownloadDocx = () => {
    if (analysisResult.docxUrl) {
      const a = document.createElement('a');
      a.href = analysisResult.docxUrl;
      a.download = 'analysis-report.docx';
      a.click();
    }
  };

  const handleDownloadMarkdown = () => {
    const blob = new Blob([analysisResult.markdownContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'analysis-report.md';
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">
                Analysis completed successfully!
              </p>
            </div>
          </div>
          <Button
            onClick={onViewResults}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            View Results
          </Button>
        </div>
      </div>

      {/* Download Options */}
      <div className="mt-6 flex flex-col sm:flex-row gap-3">
        {analysisResult.docxUrl && (
          <Button
            onClick={handleDownloadDocx}
            className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700"
          >
            <Download className="w-4 h-4" />
            <span>Download DOCX</span>
          </Button>
        )}
        
        <Button
          onClick={handleDownloadMarkdown}
          variant="outline"
          className="flex items-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>Download Markdown</span>
        </Button>

        <Link href="/features/document-analysis/history" className="sm:ml-auto">
          <Button variant="ghost" className="flex items-center space-x-2">
            <History className="w-4 h-4" />
            <span>View History</span>
          </Button>
        </Link>
      </div>

      {/* Analysis Metadata (if available) */}
      {analysisResult.metadata && (
        <div className="mt-6 pt-6 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Analysis Details</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
            {analysisResult.metadata.processingTime && (
              <div>
                <span className="text-gray-500">Processing Time:</span>
                <span className="ml-2 text-gray-900">
                  {Math.round(analysisResult.metadata.processingTime / 1000)}s
                </span>
              </div>
            )}
            {analysisResult.metadata.pageCount && (
              <div>
                <span className="text-gray-500">Pages Analyzed:</span>
                <span className="ml-2 text-gray-900">
                  {analysisResult.metadata.pageCount}
                </span>
              </div>
            )}
            {analysisResult.metadata.wordCount && (
              <div>
                <span className="text-gray-500">Word Count:</span>
                <span className="ml-2 text-gray-900">
                  {analysisResult.metadata.wordCount.toLocaleString()}
                </span>
              </div>
            )}
            {analysisResult.metadata.language && (
              <div>
                <span className="text-gray-500">Language:</span>
                <span className="ml-2 text-gray-900">
                  {analysisResult.metadata.language}
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
