/**
 * File validation and processing utilities
 */

export interface FileValidationResult {
  isValid: boolean
  error?: string
}

export interface SupportedFileType {
  mimeType: string
  extension: string
  description: string
}

export const SUPPORTED_FILE_TYPES: SupportedFileType[] = [
  {
    mimeType: 'application/pdf',
    extension: '.pdf',
    description: 'PDF Document'
  },
  {
    mimeType: 'application/msword',
    extension: '.doc',
    description: 'Microsoft Word Document (Legacy)'
  },
  {
    mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    extension: '.docx',
    description: 'Microsoft Word Document'
  },
  {
    mimeType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    extension: '.pptx',
    description: 'Microsoft PowerPoint Presentation'
  },
  {
    mimeType: 'text/plain',
    extension: '.txt',
    description: 'Plain Text File'
  }
]

export const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB

/**
 * Validate file type against supported formats
 */
export function validateFileType(file: File): FileValidationResult {
  const supportedTypes = SUPPORTED_FILE_TYPES.map(type => type.mimeType)
  
  if (!supportedTypes.includes(file.type)) {
    const supportedExtensions = SUPPORTED_FILE_TYPES.map(type => type.extension).join(', ')
    return {
      isValid: false,
      error: `Unsupported file type. Supported formats: ${supportedExtensions}`
    }
  }
  
  return { isValid: true }
}

/**
 * Validate file size against maximum limit
 */
export function validateFileSize(file: File): FileValidationResult {
  if (file.size > MAX_FILE_SIZE) {
    const maxSizeMB = Math.round(MAX_FILE_SIZE / (1024 * 1024))
    return {
      isValid: false,
      error: `File too large. Maximum size is ${maxSizeMB}MB.`
    }
  }
  
  return { isValid: true }
}

/**
 * Comprehensive file validation
 */
export function validateFile(file: File): FileValidationResult {
  // Check file type
  const typeValidation = validateFileType(file)
  if (!typeValidation.isValid) {
    return typeValidation
  }
  
  // Check file size
  const sizeValidation = validateFileSize(file)
  if (!sizeValidation.isValid) {
    return sizeValidation
  }
  
  return { isValid: true }
}

/**
 * Convert file to base64 string
 */
export async function fileToBase64(file: File): Promise<string> {
  const arrayBuffer = await file.arrayBuffer()
  return Buffer.from(arrayBuffer).toString('base64')
}

/**
 * Get file extension from filename
 */
export function getFileExtension(filename: string): string {
  return filename.slice(filename.lastIndexOf('.'))
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Check if file type supports text extraction
 */
export function supportsTextExtraction(mimeType: string): boolean {
  const textSupportedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ]
  
  return textSupportedTypes.includes(mimeType)
}
