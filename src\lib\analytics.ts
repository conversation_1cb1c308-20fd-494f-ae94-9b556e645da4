/**
 * Google Analytics 4 (GA4) Integration
 * 
 * This module provides utilities for tracking events and page views
 * with Google Analytics 4.
 */

// Extend the Window interface to include gtag
declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event' | 'js' | 'set',
      targetId: string | Date | object,
      config?: object
    ) => void;
    dataLayer: any[];
  }
}

// Get the GA Measurement ID from environment variables
export const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;

/**
 * Initialize Google Analytics
 * This should be called once when the app loads
 */
export const initGA = () => {
  if (!GA_MEASUREMENT_ID || typeof window === 'undefined') {
    return;
  }

  // Initialize dataLayer if it doesn't exist
  window.dataLayer = window.dataLayer || [];
  
  // Define gtag function
  window.gtag = function(...args) {
    window.dataLayer.push(args);
  };

  // Initialize GA
  window.gtag('js', new Date());
  window.gtag('config', GA_MEASUREMENT_ID, {
    page_title: document.title,
    page_location: window.location.href,
  });
};

/**
 * Track a page view
 * @param url - The page URL
 * @param title - The page title
 */
export const trackPageView = (url: string, title?: string) => {
  if (!GA_MEASUREMENT_ID || typeof window === 'undefined' || !window.gtag) {
    return;
  }

  window.gtag('config', GA_MEASUREMENT_ID, {
    page_title: title || document.title,
    page_location: url,
  });
};

/**
 * Track a custom event
 * @param action - The action being tracked
 * @param category - The category of the event
 * @param label - Optional label for the event
 * @param value - Optional numeric value
 */
export const trackEvent = (
  action: string,
  category: string,
  label?: string,
  value?: number
) => {
  if (!GA_MEASUREMENT_ID || typeof window === 'undefined' || !window.gtag) {
    return;
  }

  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value,
  });
};

/**
 * Track user authentication events
 */
export const trackAuth = {
  signUp: (method: string = 'email') => {
    trackEvent('sign_up', 'auth', method);
  },
  
  signIn: (method: string = 'email') => {
    trackEvent('login', 'auth', method);
  },
  
  signOut: () => {
    trackEvent('logout', 'auth');
  },
};

/**
 * Track document analysis events
 */
export const trackDocumentAnalysis = {
  start: (fileType: string, fileSize: number) => {
    trackEvent('analysis_start', 'document', fileType, fileSize);
  },
  
  complete: (fileType: string, processingTime: number) => {
    trackEvent('analysis_complete', 'document', fileType, processingTime);
  },
  
  error: (fileType: string, errorType: string) => {
    trackEvent('analysis_error', 'document', `${fileType}_${errorType}`);
  },
  
  download: (fileType: string) => {
    trackEvent('download', 'document', fileType);
  },
};

/**
 * Track subscription and billing events
 */
export const trackBilling = {
  subscriptionStart: (planName: string, amount: number) => {
    trackEvent('purchase', 'billing', planName, amount);
  },
  
  subscriptionCancel: (planName: string) => {
    trackEvent('subscription_cancel', 'billing', planName);
  },
  
  creditPurchase: (amount: number, credits: number) => {
    trackEvent('credit_purchase', 'billing', `${credits}_credits`, amount);
  },
};

/**
 * Track navigation and engagement events
 */
export const trackNavigation = {
  pageView: (pageName: string) => {
    trackEvent('page_view', 'navigation', pageName);
  },
  
  linkClick: (linkText: string, destination: string) => {
    trackEvent('link_click', 'navigation', `${linkText}_to_${destination}`);
  },
  
  buttonClick: (buttonName: string, location: string) => {
    trackEvent('button_click', 'engagement', `${buttonName}_${location}`);
  },
};

/**
 * Track user engagement events
 */
export const trackEngagement = {
  featureUsage: (featureName: string) => {
    trackEvent('feature_usage', 'engagement', featureName);
  },
  
  timeOnPage: (pageName: string, timeInSeconds: number) => {
    trackEvent('time_on_page', 'engagement', pageName, timeInSeconds);
  },
  
  scrollDepth: (percentage: number) => {
    trackEvent('scroll', 'engagement', 'depth', percentage);
  },
};

/**
 * Check if Google Analytics is enabled and available
 */
export const isGAEnabled = (): boolean => {
  return !!(GA_MEASUREMENT_ID && typeof window !== 'undefined' && window.gtag);
};

/**
 * Set user properties for analytics
 * @param properties - Object containing user properties
 */
export const setUserProperties = (properties: Record<string, any>) => {
  if (!GA_MEASUREMENT_ID || typeof window === 'undefined' || !window.gtag) {
    return;
  }

  window.gtag('set', properties);
};

/**
 * Track conversion events (for Google Ads)
 * @param conversionId - The conversion ID
 * @param conversionLabel - The conversion label
 * @param value - Optional conversion value
 */
export const trackConversion = (
  conversionId: string,
  conversionLabel: string,
  value?: number
) => {
  if (!GA_MEASUREMENT_ID || typeof window === 'undefined' || !window.gtag) {
    return;
  }

  window.gtag('event', 'conversion', {
    send_to: `${conversionId}/${conversionLabel}`,
    value: value,
    currency: 'USD',
  });
};
