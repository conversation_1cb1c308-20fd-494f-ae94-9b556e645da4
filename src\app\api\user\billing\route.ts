import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { getUserById, getTransactionsByUser } from '@/lib/db'
import { getUserCreditRenewalInfo } from '@/lib/credit-renewal'
import { getCreditPackages } from '@/lib/config'
import { stripe } from '@/lib/stripe'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    const userId = (session?.user as any)?.id
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await getUserById(userId)
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const transactions = await getTransactionsByUser(user.id, 10)
    const renewalInfo = await getUserCreditRenewalInfo(user.id)

    // Fetch subscription details from Stripe for active subscriptions
    const creditPackages = getCreditPackages()
    const subscriptionsWithStripeData = await Promise.all(
      user.subscriptions.map(async (sub) => {
        let stripeSubscription = null
        let productInfo = null

        // Get product info from stored productName and credit packages config
        if (sub.productName) {
          const creditPackage = creditPackages.find(pkg => pkg.name === sub.productName)
          if (creditPackage) {
            productInfo = {
              name: creditPackage.name,
              price: creditPackage.price,
              currency: 'HKD',
              interval: 'month',
              credits: creditPackage.credits,
            }
          }
        }

        if (sub.stripeSubscriptionId && sub.status === 'active') {
          try {
            stripeSubscription = await stripe.subscriptions.retrieve(sub.stripeSubscriptionId)
          } catch (error) {
            console.error('Error fetching Stripe subscription:', error)
          }
        }

        return {
          id: sub.id,
          status: sub.status,
          currentPeriodStart: (stripeSubscription as any)?.current_period_start
            ? new Date((stripeSubscription as any).current_period_start * 1000).toISOString()
            : sub.currentPeriodStart?.toISOString(),
          currentPeriodEnd: (stripeSubscription as any)?.current_period_end
            ? new Date((stripeSubscription as any).current_period_end * 1000).toISOString()
            : sub.currentPeriodEnd?.toISOString(),
          cancelAtPeriodEnd: (stripeSubscription as any)?.cancel_at_period_end ?? sub.cancelAtPeriodEnd,
          product: productInfo || {
            name: sub.productName || 'Unknown Plan',
            price: 0,
            currency: 'HKD',
            interval: 'month',
            credits: 0,
          },
        }
      })
    )

    const userData = {
      id: user.id,
      name: user.name,
      email: user.email,
      credits: user.credits,
      subscriptions: subscriptionsWithStripeData,
      transactions: transactions.map(tx => ({
        id: tx.id,
        type: tx.type,
        amount: tx.amount,
        currency: tx.currency,
        status: tx.status,
        description: tx.description,
        createdAt: tx.createdAt.toISOString(),
        credits: tx.credits,
      })),
      renewalInfo: renewalInfo ? {
        ...renewalInfo,
        nextRenewalDate: renewalInfo.nextRenewalDate?.toISOString() || null,
        lastRenewalDate: renewalInfo.lastRenewalDate?.toISOString() || null,
      } : null,
    }

    return NextResponse.json(userData)
  } catch (error) {
    console.error('Error fetching user billing data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
