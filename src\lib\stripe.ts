import Stripe from 'stripe'
import { env } from './config'

// Initialize Stripe with error handling for build time
export const stripe = new Stripe(env.STRIPE_SECRET_KEY || 'sk_test_dummy_key_for_build', {
  apiVersion: '2025-05-28.basil',
  typescript: true,
})

export const getStripeJs = async () => {
  const { loadStripe } = await import('@stripe/stripe-js')
  return loadStripe(env.STRIPE_PUBLISHABLE_KEY || 'pk_test_dummy_key_for_build')
}
