'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { X, Save, Upload, Trash2, Calendar } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { TemplateStorage, Template } from '@/lib/template-storage'

interface TemplateModalProps {
  isOpen: boolean
  onClose: () => void
  mode: 'save' | 'load'
  useCaseId: string | null
  currentSections: string[]
  onLoadTemplate: (sections: string[]) => void
}

export function TemplateModal({
  isOpen,
  onClose,
  mode,
  useCaseId,
  currentSections,
  onLoadTemplate
}: TemplateModalProps) {
  const [templateName, setTemplateName] = useState('')
  const [templates, setTemplates] = useState<Template[]>([])
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const loadTemplates = useCallback(async () => {
    if (!useCaseId) return
    try {
      const templates = await TemplateStorage.getTemplatesForUseCaseDB('', useCaseId)
      setTemplates(templates)
    } catch (error) {
      console.error('Error loading templates:', error)
      setError('Failed to load templates')
    }
  }, [useCaseId])

  useEffect(() => {
    if (isOpen && useCaseId) {
      loadTemplates()
      setError('')
      setSuccess('')
      setTemplateName('')
    }
  }, [isOpen, useCaseId, loadTemplates])

  const handleSaveTemplate = async () => {
    if (!useCaseId || !templateName.trim()) {
      setError('Please enter a template name')
      return
    }

    try {
      const result = await TemplateStorage.saveTemplateDB(useCaseId, templateName.trim(), currentSections)

      if (result.success) {
        setSuccess('Template saved successfully!')
        await loadTemplates() // Reload templates from database
        setTemplateName('')
        setTimeout(() => {
          onClose()
        }, 1500)
      } else {
        setError(result.error || 'Failed to save template')
      }
    } catch (error) {
      console.error('Error saving template:', error)
      setError('Failed to save template')
    }
  }

  const handleLoadTemplate = async (template: Template) => {
    onLoadTemplate(template.sections)

    // Increment usage count
    await TemplateStorage.incrementUsageDB(template.id)

    setSuccess('Template loaded successfully!')
    setTimeout(() => {
      onClose()
    }, 1000)
  }

  const handleDeleteTemplate = async (templateId: string) => {
    try {
      const success = await TemplateStorage.deleteTemplateDB(templateId)
      if (success) {
        await loadTemplates() // Reload templates from database
      } else {
        setError('Failed to delete template')
      }
    } catch (error) {
      console.error('Error deleting template:', error)
      setError('Failed to delete template')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (!isOpen) return null

  return (
    <div className="auth-modal-backdrop flex items-center justify-center p-4">
      <div
        className="absolute inset-0"
        onClick={onClose}
      />
      <div className="auth-modal-content max-w-md w-full max-h-[80vh] overflow-hidden relative z-10">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {mode === 'save' ? 'Save Template' : 'Load Template'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {mode === 'save' ? (
            <div className="space-y-4">
              <div>
                <label htmlFor="templateName" className="block text-sm font-medium text-gray-700 mb-2">
                  Template Name
                </label>
                <input
                  id="templateName"
                  type="text"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  placeholder="Enter template name..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  autoFocus
                />
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Current Configuration</h3>
                <p className="text-sm text-gray-600 mb-2">{currentSections.length} sections</p>
                <div className="max-h-32 overflow-y-auto">
                  <ul className="text-xs text-gray-500 space-y-1">
                    {currentSections.map((section, index) => (
                      <li key={index}>• {section}</li>
                    ))}
                  </ul>
                </div>
              </div>

              <p className="text-xs text-gray-500">
                You can save up to {TemplateStorage.getMaxTemplatesPerUseCase()} templates per use case.
                Current: {templates.length}/{TemplateStorage.getMaxTemplatesPerUseCase()}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {templates.length === 0 ? (
                <div className="text-center py-8">
                  <Upload className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No saved templates found</p>
                  <p className="text-sm text-gray-400">Save a template first to load it later</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {templates.map((template) => (
                    <div
                      key={template.id}
                      className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900">{template.name}</h3>
                          <p className="text-sm text-gray-600 mt-1">
                            {template.sections.length} sections
                            {template.usageCount !== undefined && (
                              <span className="ml-2 text-purple-600">• Used {template.usageCount} times</span>
                            )}
                          </p>
                          <div className="flex items-center text-xs text-gray-400 mt-2">
                            <Calendar className="w-3 h-3 mr-1" />
                            {formatDate(template.createdAt)}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 ml-4">
                          <Button
                            onClick={() => handleLoadTemplate(template)}
                            size="sm"
                            className="bg-purple-600 hover:bg-purple-700 text-white"
                          >
                            Load
                          </Button>
                          <button
                            onClick={() => handleDeleteTemplate(template.id)}
                            className="p-1 text-red-500 hover:bg-red-100 rounded"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Messages */}
          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}
          
          {success && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          )}
        </div>

        {/* Footer */}
        {mode === 'save' && (
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
            <Button
              onClick={onClose}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveTemplate}
              className="bg-purple-600 hover:bg-purple-700 text-white"
              disabled={!templateName.trim()}
            >
              <Save className="w-4 h-4 mr-2" />
              Save Template
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
