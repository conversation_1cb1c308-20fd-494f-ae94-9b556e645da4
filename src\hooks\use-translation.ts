import { useState, useEffect } from 'react'
import { useLanguage } from '@/contexts/language-context'

// Static imports for all translation files
import enCommon from '../../locales/en/common.json'
import zhCommon from '../../locales/zh/common.json'
import enHomepage from '../../locales/en/homepage.json'
import zhHomepage from '../../locales/zh/homepage.json'
import enTrial from '../../locales/en/trial.json'
import zhTrial from '../../locales/zh/trial.json'
import enPricing from '../../locales/en/pricing.json'
import zhPricing from '../../locales/zh/pricing.json'
import enContact from '../../locales/en/contact.json'
import zhContact from '../../locales/zh/contact.json'

// Translation types
export type TranslationKey = string
export type TranslationParams = Record<string, string | number>

// Translation namespace type
export interface TranslationNamespace {
  [key: string]: string | string[] | TranslationNamespace | any
}

// Supported locales
export type SupportedLocale = 'en' | 'zh'

// Translation cache
const translationCache: Record<string, TranslationNamespace> = {}

/**
 * Load translation file for a specific locale and namespace
 */
function loadTranslation(locale: SupportedLocale, namespace: string): TranslationNamespace {
  const cacheKey = `${locale}-${namespace}`

  if (translationCache[cacheKey]) {
    return translationCache[cacheKey]
  }

  try {
    let translation
    // Static selection based on locale and namespace
    if (locale === 'en' && namespace === 'common') {
      translation = enCommon
    } else if (locale === 'zh' && namespace === 'common') {
      translation = zhCommon
    } else if (locale === 'en' && namespace === 'homepage') {
      translation = enHomepage
    } else if (locale === 'zh' && namespace === 'homepage') {
      translation = zhHomepage
    } else if (locale === 'en' && namespace === 'trial') {
      translation = enTrial
    } else if (locale === 'zh' && namespace === 'trial') {
      translation = zhTrial
    } else if (locale === 'en' && namespace === 'pricing') {
      translation = enPricing
    } else if (locale === 'zh' && namespace === 'pricing') {
      translation = zhPricing
    } else if (locale === 'en' && namespace === 'contact') {
      translation = enContact
    } else if (locale === 'zh' && namespace === 'contact') {
      translation = zhContact
    } else {
      throw new Error(`Translation not found: ${locale}/${namespace}`)
    }

    translationCache[cacheKey] = translation
    return translationCache[cacheKey]
  } catch (error) {
    console.warn(`Failed to load translation: ${locale}/${namespace}`, error)
    // Fallback to English if available
    if (locale !== 'en') {
      try {
        let fallback
        if (namespace === 'common') {
          fallback = enCommon
        } else if (namespace === 'homepage') {
          fallback = enHomepage
        } else if (namespace === 'trial') {
          fallback = enTrial
        } else if (namespace === 'pricing') {
          fallback = enPricing
        } else if (namespace === 'contact') {
          fallback = enContact
        }

        if (fallback) {
          return fallback
        }
      } catch (fallbackError) {
        console.error(`Failed to load fallback translation: en/${namespace}`, fallbackError)
      }
    }
    return {}
  }
}

/**
 * Get nested value from object using dot notation
 */
function getNestedValue(obj: TranslationNamespace, path: string): string | undefined {
  return path.split('.').reduce((current: any, key: string) => {
    if (!current) return undefined

    // Handle array indices
    if (Array.isArray(current) && !isNaN(Number(key))) {
      return current[Number(key)]
    }

    // Handle object properties
    if (typeof current === 'object') {
      return current[key]
    }

    return undefined
  }, obj) as string | undefined
}

/**
 * Replace parameters in translation string
 */
function interpolate(text: string, params: TranslationParams = {}): string {
  return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return params[key]?.toString() || match
  })
}

/**
 * Get current locale from URL or default to 'en'
 */
function getCurrentLocale(): SupportedLocale {
  if (typeof window === 'undefined') {
    return 'en' // Default for SSR
  }

  try {
    // For App Router, we'll detect locale from pathname or localStorage
    const pathname = window.location.pathname
    const segments = pathname.split('/')

    // Check if first segment is a locale
    if (segments[1] && isSupportedLocale(segments[1])) {
      return segments[1]
    }

    // Fallback to saved preference or browser detection
    const saved = localStorage.getItem('docuchamp-language-preference')
    if (saved && isSupportedLocale(saved)) {
      return saved
    }

    // Browser language detection
    const browserLang = navigator.language.toLowerCase()
    if (browserLang.startsWith('zh')) {
      return 'zh'
    }
  } catch (error) {
    console.warn('Error detecting locale:', error)
  }

  return 'en'
}

/**
 * Custom hook for translations
 */
export function useTranslation(namespace: string = 'common') {
  const { locale } = useLanguage()

  // Load translations for current locale and namespace
  const [translations, setTranslations] = useState<TranslationNamespace>({})
  const [isReady, setIsReady] = useState(false)

  useEffect(() => {
    const cacheKey = `${locale}-${namespace}`

    // Return cached translations if available
    if (translationCache[cacheKey]) {
      setTranslations(translationCache[cacheKey])
      setIsReady(true)
      return
    }

    // Load translations synchronously
    try {
      const data = loadTranslation(locale, namespace)
      translationCache[cacheKey] = data
      setTranslations(data)
      setIsReady(true)
    } catch (error) {
      console.error(`Failed to load translation: ${locale}/${namespace}`, error)
      setTranslations({})
      setIsReady(true) // Still set ready even on error to prevent infinite loading
    }
  }, [locale, namespace])

  /**
   * Translate function
   */
  const t = (key: TranslationKey, params?: TranslationParams): any => {
    const value = getNestedValue(translations, key)

    if (value !== undefined) {
      if (typeof value === 'string') {
        return interpolate(value, params)
      }
      // Return arrays or objects as-is for complex data structures
      return value
    }

    // Fallback: return empty string if not ready, or the key if translation not found
    if (!isReady) {
      return '' // Return empty string while loading to prevent flash of translation keys
    }
    console.warn(`Translation missing: ${locale}.${namespace}.${key}`)
    return key
  }

  /**
   * Check if translation exists
   */
  const exists = (key: TranslationKey): boolean => {
    return getNestedValue(translations, key) !== undefined
  }

  return {
    t,
    exists,
    locale,
    isReady
  }
}

/**
 * Preload translations for better performance
 */
export function preloadTranslations(locale: SupportedLocale, namespaces: string[] = ['common']) {
  namespaces.forEach(namespace => loadTranslation(locale, namespace))
}

/**
 * Get available locales
 */
export function getAvailableLocales(): SupportedLocale[] {
  return ['en', 'zh']
}

/**
 * Check if locale is supported
 */
export function isSupportedLocale(locale: string): locale is SupportedLocale {
  return getAvailableLocales().includes(locale as SupportedLocale)
}
