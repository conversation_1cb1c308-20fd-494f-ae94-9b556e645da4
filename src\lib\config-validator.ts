import { config, env } from './config'

interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

export function validateConfiguration(): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // Validate app configuration
  if (!config.app.name) {
    errors.push('App name is required')
  }

  if (!config.app.description) {
    warnings.push('App description is recommended')
  }

  // Validate billing configuration
  if (config.billing.enableFixedPlans && config.subscriptionPlans.length === 0) {
    errors.push('At least one subscription plan is required when fixed plans are enabled')
  }

  if (config.billing.enableMeteredBilling && config.billing.creditPackages.length === 0) {
    errors.push('At least one credit package is required when metered billing is enabled')
  }

  // Validate subscription plans
  config.subscriptionPlans.forEach((plan, index) => {
    if (!plan.stripePriceId) {
      errors.push(`Subscription plan ${index + 1} is missing Stripe price ID`)
    }
    if (plan.price <= 0) {
      errors.push(`Subscription plan ${index + 1} must have a positive price`)
    }
    if (!plan.features || plan.features.length === 0) {
      warnings.push(`Subscription plan ${index + 1} has no features listed`)
    }
  })

  // Validate credit packages
  config.billing.creditPackages.forEach((pkg, index) => {
    if (pkg.credits <= 0) {
      errors.push(`Credit package ${index + 1} must have positive credits`)
    }
    if (pkg.price <= 0) {
      errors.push(`Credit package ${index + 1} must have a positive price`)
    }
  })

  // Validate environment variables
  const requiredEnvVars = [
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'STRIPE_PUBLISHABLE_KEY',
    'STRIPE_SECRET_KEY',
    'STRIPE_WEBHOOK_SECRET',
    'JWT_SECRET',
    'DOCUMENT_ANALYSIS_API_ENDPOINT',
    'DOCUMENT_ANALYSIS_AUTH_SECRET'
  ]

  requiredEnvVars.forEach(envVar => {
    if (!env[envVar as keyof typeof env]) {
      errors.push(`Environment variable ${envVar} is required`)
    }
  })

  // Validate document analysis feature
  if (config.features.documentAnalysis.enabled && !config.features.documentAnalysis.creditCost) {
    warnings.push('Document analysis is enabled but has no credit cost defined')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

export function getConfigurationSummary() {
  return {
    app: {
      name: config.app.name,
      billingModel: config.billing.model,
      enabledFeatures: config.features.documentAnalysis.enabled ? ['documentAnalysis'] : [],
      subscriptionPlans: config.subscriptionPlans.length,
      creditPackages: config.billing.creditPackages.length,
    },
    features: {
      documentAnalysis: {
        enabled: config.features.documentAnalysis.enabled,
        creditCost: config.features.documentAnalysis.creditCost,
      }
    }
  }
}

export function validateFeatureAccess(featureName: string, userSubscription?: any, userCredits?: number): {
  hasAccess: boolean
  reason?: string
  requiredCredits?: number
} {
  // Only support document analysis for now
  if (featureName !== 'documentAnalysis') {
    return { hasAccess: false, reason: 'Feature not found' }
  }

  if (!config.features.documentAnalysis.enabled) {
    return { hasAccess: false, reason: 'Feature is disabled' }
  }

  // Check if user has active subscription that includes this feature
  if (userSubscription && userSubscription.status === 'active') {
    return { hasAccess: true }
  }

  // Check if user has enough credits for metered billing
  if (config.billing.enableMeteredBilling && config.features.documentAnalysis.creditCost) {
    if (userCredits === undefined) {
      return { hasAccess: false, reason: 'Credit balance unknown' }
    }

    if (userCredits < config.features.documentAnalysis.creditCost) {
      return {
        hasAccess: false,
        reason: 'Insufficient credits',
        requiredCredits: config.features.documentAnalysis.creditCost
      }
    }

    return { hasAccess: true }
  }

  return { hasAccess: false, reason: 'No valid subscription or credits' }
}
