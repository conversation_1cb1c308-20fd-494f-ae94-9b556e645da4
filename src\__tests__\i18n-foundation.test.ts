/**
 * @jest-environment jsdom
 */

import { 
  getAvailableLocales, 
  isSupportedLocale 
} from '@/hooks/use-translation'

import {
  detectBrowserLanguage,
  saveLanguagePreference,
  getSavedLanguagePreference,
  clearLanguagePreference,
  getLanguageDisplayName,
  getLanguageFlag
} from '@/lib/language-detection'

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock navigator
const navigatorMock = {
  language: 'en-US',
  languages: ['en-US', 'en']
}

Object.defineProperty(window, 'navigator', {
  value: navigatorMock,
  writable: true
})

describe('i18n Foundation Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  describe('Translation utilities', () => {
    it('should return correct available locales', () => {
      const locales = getAvailableLocales()
      expect(locales).toEqual(['en', 'zh'])
      expect(locales).toHaveLength(2)
    })

    it('should validate supported locales correctly', () => {
      expect(isSupportedLocale('en')).toBe(true)
      expect(isSupportedLocale('zh')).toBe(true)
      expect(isSupportedLocale('fr')).toBe(false)
      expect(isSupportedLocale('es')).toBe(false)
      expect(isSupportedLocale('')).toBe(false)
      expect(isSupportedLocale('invalid')).toBe(false)
    })
  })

  describe('Language detection', () => {
    it('should detect English from browser language', () => {
      navigatorMock.language = 'en-US'
      navigatorMock.languages = ['en-US', 'en-GB']
      
      const result = detectBrowserLanguage()
      expect(result).toBe('en')
    })

    it('should detect Chinese from various browser languages', () => {
      const chineseVariants = [
        'zh-TW',
        'zh-HK', 
        'zh-CN',
        'zh-SG',
        'zh-Hant',
        'zh-Hans'
      ]

      chineseVariants.forEach(lang => {
        navigatorMock.language = lang
        const result = detectBrowserLanguage()
        expect(result).toBe('zh')
      })
    })

    it('should return null for unsupported languages', () => {
      navigatorMock.language = 'fr-FR'
      navigatorMock.languages = ['fr-FR', 'de-DE']
      
      const result = detectBrowserLanguage()
      expect(result).toBe(null)
    })
  })

  describe('Language preference storage', () => {
    it('should save and retrieve language preferences', () => {
      // Test saving
      saveLanguagePreference('zh')
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'docuchamp-language-preference',
        'zh'
      )

      // Test retrieving
      localStorageMock.getItem.mockReturnValue('zh')
      const result = getSavedLanguagePreference()
      expect(result).toBe('zh')
      expect(localStorageMock.getItem).toHaveBeenCalledWith('docuchamp-language-preference')
    })

    it('should handle invalid saved preferences', () => {
      localStorageMock.getItem.mockReturnValue('invalid-locale')
      
      const result = getSavedLanguagePreference()
      expect(result).toBe(null)
    })

    it('should clear language preferences', () => {
      clearLanguagePreference()
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('docuchamp-language-preference')
    })
  })

  describe('Language display utilities', () => {
    it('should return correct display names', () => {
      expect(getLanguageDisplayName('en')).toBe('English')
      expect(getLanguageDisplayName('zh')).toBe('繁體中文')
    })

    it('should return correct language flags', () => {
      expect(getLanguageFlag('en')).toBe('🇺🇸')
      expect(getLanguageFlag('zh')).toBe('🇭🇰')
    })

    it('should handle unknown locales gracefully', () => {
      expect(getLanguageDisplayName('unknown' as any)).toBe('unknown')
      expect(getLanguageFlag('unknown' as any)).toBe('🌐')
    })
  })

  describe('Configuration validation', () => {
    it('should have consistent locale configuration', () => {
      const availableLocales = getAvailableLocales()
      
      // Each locale should be supported
      availableLocales.forEach(locale => {
        expect(isSupportedLocale(locale)).toBe(true)
      })

      // Should have display names for all locales
      availableLocales.forEach(locale => {
        const displayName = getLanguageDisplayName(locale)
        expect(displayName).toBeTruthy()
        expect(typeof displayName).toBe('string')
      })

      // Should have flags for all locales
      availableLocales.forEach(locale => {
        const flag = getLanguageFlag(locale)
        expect(flag).toBeTruthy()
        expect(typeof flag).toBe('string')
      })
    })
  })
})
