# API Specification: Webhook - Analysis Complete


## Base URL

```
https://localhost:3000
```

**Endpoint:** `/api/webhooks/analysis-complete`

## POST /api/webhooks/analysis-complete

Receives webhook notifications when a document analysis operation is completed or failed. Verifies the request authentication, updates the usage record, and returns a status response.

### Authentication
- **Method**: <PERSON><PERSON>
- **Header**: `Authorization: Bearer <WEBHOOK_ANALYSIS_COMPLETE_KEY>`
- The webhook secret is configured in the environment variable `WEBHOOK_ANALYSIS_COMPLETE_KEY`

### Request Headers
- `Authorization` (string, required): Bearer token for webhook authentication
- `Content-Type` (string, required): Must be `application/json`

### Request Body (JSON)
```json
{
  "operationId": "string",           // Required. Unique identifier for the analysis operation.
  "userId": "string",                // Required. User ID associated with the operation.
  "success": "boolean",              // Required. True if analysis completed successfully, false if failed.
  "markdownContent": "string",       // Optional. Markdown content of the analysis results for preview display (required if success is true).
  "error": "string",                 // Optional. Error message (required if success is false).
  "metadata": {                      // Optional. Additional metadata about the analysis.
    "fileName": "string",            // Original file name
    "fileSize": "number",            // File size in bytes
    "analysisType": "string",        // Type of analysis performed
    "sections": ["string"],          // Array of sections included in analysis
    "language": "string",            // Detected document language
    "reportLength": "string",        // Report length setting (short/medium/long)
    "processedAt": "string",         // ISO timestamp when processing completed
    "processingTimeMs": "number",    // Processing time in milliseconds
    "pageCount": "number",           // Number of pages in document
    "wordCount": "number",           // Total word count
    "hasDocx": "boolean",            // Whether DOCX output is available
    "costBreakdown": {               // Cost analysis details
      "input_tokens": "number",      // Number of input tokens
      "output_tokens": "number",     // Number of output tokens
      "total_cost": "number",        // Total cost in USD
      "model_used": "string"         // AI model used for analysis
    }
  },
  "output_file_blob_sas_url": "string" // Optional. Direct download URL to the processed DOCX file (required if success is true).
}
```

### Field Details

#### Required Fields
- **operationId**: UUID v4 string that matches the operation ID from the initial analysis request
- **userId**: User ID that must match the user who initiated the analysis operation
- **success**: Boolean indicating whether the analysis completed successfully

#### Success-Dependent Fields
- **For successful completion** (`success: true`):
  - **markdownContent**: Markdown-formatted analysis results for immediate preview in the UI
  - **output_file_blob_sas_url**: Direct download URL to the processed DOCX file (Azure Blob Storage with SAS token)
  - **metadata**: Comprehensive analysis metadata including cost breakdown and processing details

- **For failed processing** (`success: false`):
  - **error**: Descriptive error message explaining the failure reason

#### Frontend Integration
When the webhook is received:
1. The **markdownContent** is displayed in a modal with markdown rendering for immediate preview
2. The **output_file_blob_sas_url** provides a download button for the full DOCX report
3. Real-time notifications are sent via Server-Sent Events (SSE) to update the UI
4. The usage record is updated with completion status and metadata

### Example Request - Successful Analysis
```bash
curl -X POST https://your-app.com/api/webhooks/analysis-complete \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer zRV8-AsxwZoUrnkZC^n88DcH!%}jw7Ag" \
  -d '{
    "operationId": "123e4567-e89b-12d3-a456-426614174000",
    "userId": "user123",
    "success": true,
    "markdownContent": "# 📊 AI Analysis Summary\\n📄 Document: sample.pdf\\n🎯 Use Case: Document Analysis\\n📑 Pages: 5\\n📦 Chunks: 2\\n🖼️ Image Pages: 3\\n📝 Text Pages: 2\\n🎯 Tokens: 15,234\\n💰 Cost: $0.0456\\n⏱️ Time: 12.3s\\n\\n---\\n\\n# Executive Summary\\n\\nThis document provides...\\n\\n## Key Findings\\n\\n• Finding 1\\n• Finding 2\\n\\n## Recommendations\\n\\n1. Recommendation 1\\n2. Recommendation 2",
    "metadata": {
      "fileName": "sample.pdf",
      "fileSize": 2048576,
      "analysisType": "document_analysis",
      "sections": ["executive_summary", "key_findings", "recommendations"],
      "language": "English",
      "reportLength": "long",
      "processedAt": "2024-01-15T10:30:45.123Z",
      "processingTimeMs": 12345,
      "pageCount": 5,
      "wordCount": 1250,
      "hasDocx": true,
      "costBreakdown": {
        "input_tokens": 12000,
        "output_tokens": 3234,
        "total_cost": 0.0456,
        "model_used": "gpt-4o"
      }
    },
    "output_file_blob_sas_url": "https://storage.blob.core.windows.net/container/sample-ai-analyzed.docx?sv=2022-11-02&ss=b&srt=sco&sp=r&se=2024-03-15T10:30:45Z&st=2024-01-15T10:30:45Z&spr=https&sig=..."
  }'
```

### Example Request - Failed Analysis
```bash
curl -X POST https://your-app.com/api/webhooks/analysis-complete \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer zRV8-AsxwZoUrnkZC^n88DcH!%}jw7Ag" \
  -d '{
    "operationId": "123e4567-e89b-12d3-a456-426614174000",
    "userId": "user123",
    "success": false,
    "error": "Document format not supported or file corrupted",
    "metadata": {
      "fileName": "sample.pdf",
      "fileSize": 2048576,
      "analysisType": "document_analysis",
      "processedAt": "2025-07-11T10:25:00.000Z",
      "processingTimeMs": 1200
    }
  }'
```

### Responses
- `200 OK`: Usage record updated successfully.
  ```json
  {
    "success": true,
    "operationId": "string",
    "status": "string",
    "message": "Usage record updated successfully"
  }
  ```
- `400 Bad Request`: Missing required fields.
  ```json
  { "error": "Missing required fields" }
  ```
- `401 Unauthorized`: Invalid or missing authentication token.
  ```json
  { "error": "Unauthorized" }
  ```
- `403 Forbidden`: User ID mismatch.
  ```json
  { "error": "User ID mismatch" }
  ```
- `404 Not Found`: Operation not found.
  ```json
  { "error": "Operation not found" }
  ```
- `500 Internal Server Error`: Unexpected error.
  ```json
  { "error": "Internal server error", "details": "string" }
  ```

---

## Implementation Details

### Real-Time Updates via Server-Sent Events (SSE)

When the webhook is received and processed successfully, the system:

1. **Updates the database** - Stores the analysis results in the usage record
2. **Triggers SSE notification** - Sends real-time update to the frontend via `/api/analyze/{operationId}/stream`
3. **Frontend response** - The UI immediately:
   - Shows a success notification
   - Displays the markdown content in a modal viewer
   - Provides download button for the DOCX file
   - Updates the analysis progress to 100%

### SSE Message Format

The SSE stream sends the following message when analysis completes:

```json
{
  "type": "analysis_complete",
  "operationId": "123e4567-e89b-12d3-a456-426614174000",
  "success": true,
  "markdownContent": "# 📊 AI Analysis Summary\\n📄 Document: sample.pdf...",
  "output_file_blob_sas_url": "https://storage.blob.core.windows.net/container/sample-ai-analyzed.docx?sv=2022-11-02&ss=b&srt=sco&sp=r&se=2024-03-15T10:30:45Z&st=2024-01-15T10:30:45Z&spr=https&sig=...",
  "metadata": {
    "fileName": "sample.pdf",
    "fileSize": 2048576,
    "analysisType": "document_analysis",
    "sections": ["executive_summary", "key_findings", "recommendations"],
    "language": "English",
    "reportLength": "long",
    "processedAt": "2024-01-15T10:30:45.123Z",
    "processingTimeMs": 12345,
    "pageCount": 5,
    "wordCount": 1250,
    "hasDocx": true,
    "costBreakdown": {
      "input_tokens": 12000,
      "output_tokens": 3234,
      "total_cost": 0.0456,
      "model_used": "gpt-4o"
    }
  }
}
```

### Frontend Integration Flow

1. **User clicks "Analyze" button** → Analysis request sent to `/api/analyze`
2. **SSE connection established** → Frontend connects to `/api/analyze/{operationId}/stream`
3. **Progress tracking** → UI shows progress bar (estimated based on file size)
4. **Webhook received** → Azure Function sends completion webhook
5. **Database updated** → Usage record updated with results
6. **SSE notification sent** → Real-time update pushed to frontend
7. **UI updates** → Modal opens with markdown preview and download link

---

## GET /api/webhooks/analysis-complete

Returns a simple message and timestamp for testing the endpoint.

### Response
- `200 OK`:
  ```json
  {
    "message": "Analysis completion webhook endpoint",
    "timestamp": "2025-07-09T00:00:00.000Z"
  }
  ```
