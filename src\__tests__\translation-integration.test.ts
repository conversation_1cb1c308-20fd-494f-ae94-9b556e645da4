/**
 * @jest-environment jsdom
 */

import { getAvailableLocales, isSupportedLocale } from '@/hooks/use-translation'

describe('Translation Integration', () => {
  it('should have correct locale configuration', () => {
    const locales = getAvailableLocales()
    expect(locales).toEqual(['en', 'zh'])
    
    expect(isSupportedLocale('en')).toBe(true)
    expect(isSupportedLocale('zh')).toBe(true)
    expect(isSupportedLocale('fr')).toBe(false)
  })

  it('should have translation files structure', async () => {
    // Test that translation files can be imported
    try {
      const enCommon = await import('../../locales/en/common.json')
      const zhCommon = await import('../../locales/zh/common.json')
      const enHomepage = await import('../../locales/en/homepage.json')
      const zhHomepage = await import('../../locales/zh/homepage.json')
      
      expect(enCommon.default).toBeDefined()
      expect(zhCommon.default).toBeDefined()
      expect(enHomepage.default).toBeDefined()
      expect(zhHomepage.default).toBeDefined()
      
      // Check basic structure
      expect(enCommon.default.navigation).toBeDefined()
      expect(enCommon.default.buttons).toBeDefined()
      expect(enHomepage.default.hero).toBeDefined()
      expect(enHomepage.default.banner).toBeDefined()
      
      expect(zhCommon.default.navigation).toBeDefined()
      expect(zhCommon.default.buttons).toBeDefined()
      expect(zhHomepage.default.hero).toBeDefined()
      expect(zhHomepage.default.banner).toBeDefined()
    } catch (error) {
      fail(`Translation files should be importable: ${error}`)
    }
  })

  it('should have consistent translation keys between languages', async () => {
    const enHomepage = await import('../../locales/en/homepage.json')
    const zhHomepage = await import('../../locales/zh/homepage.json')
    
    const enKeys = Object.keys(enHomepage.default)
    const zhKeys = Object.keys(zhHomepage.default)
    
    expect(enKeys.sort()).toEqual(zhKeys.sort())
    
    // Check nested structure consistency
    expect(Object.keys(enHomepage.default.hero)).toEqual(Object.keys(zhHomepage.default.hero))
    expect(Object.keys(enHomepage.default.banner)).toEqual(Object.keys(zhHomepage.default.banner))
  })

  it('should have all required homepage translation keys', async () => {
    const enHomepage = await import('../../locales/en/homepage.json')
    const homepage = enHomepage.default
    
    // Check required sections exist
    expect(homepage.banner).toBeDefined()
    expect(homepage.hero).toBeDefined()
    expect(homepage.videoSection).toBeDefined()
    expect(homepage.whySection).toBeDefined()
    expect(homepage.cta).toBeDefined()
    
    // Check required hero keys
    expect(homepage.hero.title).toBeDefined()
    expect(homepage.hero.subtitle).toBeDefined()
    expect(homepage.hero.description).toBeDefined()
    expect(homepage.hero.buttons).toBeDefined()
    expect(homepage.hero.documentTypes).toBeDefined()
    
    // Check document types array
    expect(Array.isArray(homepage.hero.documentTypes)).toBe(true)
    expect(homepage.hero.documentTypes.length).toBe(6)
    
    // Check button keys
    expect(homepage.hero.buttons.getStartedFree).toBeDefined()
    expect(homepage.hero.buttons.viewPricing).toBeDefined()
  })

  it('should have valid Chinese translations', async () => {
    const zhHomepage = await import('../../locales/zh/homepage.json')
    const homepage = zhHomepage.default
    
    // Check that Chinese translations are not empty and contain Chinese characters
    expect(homepage.banner.limitedOffer).toMatch(/[\u4e00-\u9fff]/) // Contains Chinese characters
    expect(homepage.hero.subtitle).toMatch(/[\u4e00-\u9fff]/)
    expect(homepage.hero.description).toMatch(/[\u4e00-\u9fff]/)
    
    // Check button translations
    expect(homepage.hero.buttons.getStartedFree).toMatch(/[\u4e00-\u9fff]/)
    expect(homepage.hero.buttons.viewPricing).toMatch(/[\u4e00-\u9fff]/)
  })
})
