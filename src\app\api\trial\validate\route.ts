import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

const TRIAL_MAX_ATTEMPTS = 3
const TRIAL_RESET_HOURS = 24 // Reset trials every 24 hours per device fingerprint

export async function POST(request: NextRequest) {
  try {
    const { fingerprint, userId } = await request.json()

    if (!fingerprint) {
      return NextResponse.json({ error: 'Missing fingerprint' }, { status: 400 })
    }

    // Get current time and reset window start time
    const now = new Date()
    const resetWindowStart = new Date(now.getTime() - (TRIAL_RESET_HOURS * 60 * 60 * 1000))

    // GDPR Compliance: Automatically delete old trial usage records (older than 24 hours)
    await prisma.trialUsage.deleteMany({
      where: {
        createdAt: {
          lt: resetWindowStart
        }
      }
    })

    // Enhanced validation: Check multiple criteria to prevent abuse

    // 1. Count by exact fingerprint match
    const exactFingerprintCount = await prisma.trialUsage.count({
      where: {
        fingerprint: fingerprint,
        createdAt: {
          gte: resetWindowStart
        }
      }
    })

    // 2. Check for similar fingerprints (to catch slight variations)
    // Extract the base fingerprint hash (before the entropy part)
    const baseFingerprintHash = fingerprint.split('_')[0]
    const similarFingerprintCount = await prisma.trialUsage.count({
      where: {
        fingerprint: {
          startsWith: baseFingerprintHash
        },
        createdAt: {
          gte: resetWindowStart
        }
      }
    })

    // 3. Check for rapid successive attempts (rate limiting)
    const recentAttempts = await prisma.trialUsage.count({
      where: {
        fingerprint: fingerprint,
        createdAt: {
          gte: new Date(now.getTime() - (5 * 60 * 1000)) // Last 5 minutes
        }
      }
    })

    // 4. Check for suspicious patterns (same userId with different fingerprints)
    let suspiciousUserActivity = 0
    if (userId) {
      suspiciousUserActivity = await prisma.trialUsage.count({
        where: {
          userId: userId,
          fingerprint: {
            not: fingerprint
          },
          createdAt: {
            gte: resetWindowStart
          }
        }
      })
    }

    // Apply the most restrictive count
    const effectiveUsageCount = Math.max(exactFingerprintCount, similarFingerprintCount)

    // Additional checks for abuse prevention
    const isRateLimited = recentAttempts >= 2 // Max 2 attempts in 5 minutes
    const isSuspiciousUser = suspiciousUserActivity >= 2 // Max 2 different fingerprints per userId

    const remainingAttempts = Math.max(0, TRIAL_MAX_ATTEMPTS - effectiveUsageCount)
    const canUseTrial = remainingAttempts > 0 && !isRateLimited && !isSuspiciousUser

    let message = ''
    if (!canUseTrial) {
      if (isRateLimited) {
        message = 'Too many attempts. Please wait a few minutes before trying again.'
      } else if (isSuspiciousUser) {
        message = 'Suspicious activity detected. Please sign up for a free account.'
      } else {
        message = 'Trial limit reached for this device. Trials reset every 24 hours.'
      }
    } else {
      message = `${remainingAttempts} trial${remainingAttempts !== 1 ? 's' : ''} remaining`
    }

    return NextResponse.json({
      canUseTrial,
      remainingAttempts: canUseTrial ? remainingAttempts : 0,
      usageCount: effectiveUsageCount,
      resetWindowStart: resetWindowStart.toISOString(),
      fingerprint,
      message,
      debug: {
        exactCount: exactFingerprintCount,
        similarCount: similarFingerprintCount,
        recentAttempts,
        suspiciousUserActivity,
        isRateLimited,
        isSuspiciousUser
      }
    })

  } catch (error) {
    console.error('Trial validation error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        canUseTrial: false,
        remainingAttempts: 0,
        message: 'Validation failed'
      },
      { status: 500 }
    )
  }
}
