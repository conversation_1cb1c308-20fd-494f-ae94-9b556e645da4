import type { Metadata } from 'next'
import { generateMetadata, structuredData } from '@/lib/seo-config'

export const metadata: Metadata = generateMetadata({
  title: 'Pricing Plans - Affordable AI Document Processing',
  description: 'Choose the perfect plan for your document processing needs. Flexible pricing for individuals, teams, and enterprises. Start free with 10 credits. Premium plans from HK$380/month.',
  keywords: [
    'DocuChampAI pricing',
    'AI document processing plans',
    'PDF automation cost',
    'document analysis pricing',
    'enterprise document solutions',
    'affordable AI tools',
    'document processing subscription',
    'bulk document pricing',
    'professional document analysis',
    'office productivity pricing',
    'monthly credit packages',
    'subscription plans',
    'document analysis costs',
    'AI analysis pricing',
    'business document plans'
  ],
})

export default function PricingLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData.pricing) }}
      />
      {children}
    </>
  )
}
