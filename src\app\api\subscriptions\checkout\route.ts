import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { getUserById, updateUserStripeCustomerId } from '@/lib/db'
import { createStripeCustomer, createSubscriptionCheckout } from '@/lib/stripe-utils'
import { stripe } from '@/lib/stripe'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const userId = (session?.user as any)?.id
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { priceId } = await request.json()
    if (!priceId) {
      return NextResponse.json({ error: 'Price ID is required' }, { status: 400 })
    }

    const user = await getUserById(userId)
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Get or create Stripe customer
    let customerId: string

    // Check if user already has a Stripe customer ID
    if (user.stripeCustomerId) {
      customerId = user.stripeCustomerId
    } else {
      // Create new Stripe customer
      const customer = await createStripeCustomer(user.email, user.name || undefined)
      customerId = customer.id

      // Update customer metadata with user ID and store customer ID on user
      await stripe.customers.update(customerId, {
        metadata: {
          userId: user.id,
        },
      })

      // Store the customer ID on the user record
      await updateUserStripeCustomerId(user.id, customerId)
    }

    // Create checkout session
    const checkoutSession = await createSubscriptionCheckout(
      customerId,
      priceId,
      `${process.env.NEXTAUTH_URL}/billing?success=true`,
      `${process.env.NEXTAUTH_URL}/pricing?canceled=true`
    )

    return NextResponse.json({ url: checkoutSession.url })
  } catch (error) {
    console.error('Checkout error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
