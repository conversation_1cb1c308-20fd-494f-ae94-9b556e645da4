import appConfig from '../../config/app.config.json';

export interface AppConfig {
  app: {
    name: string;
    description: string;
    logo: string;
    favicon: string;
    primaryColor: string;
    secondaryColor: string;
  };
  features: {
    enableSignup: boolean;
    enableEmailVerification: boolean;
    enablePasswordReset: boolean;
    enableSocialLogin: boolean;
    enableFileUpload: boolean;
    maxFileSize: number;
    allowedFileTypes: string[];
    documentAnalysis: {
      enabled: boolean;
      creditCost: number;
    };
  };
  billing: {
    model: 'fixed' | 'metered' | 'hybrid';
    currency: string;
    enableFixedPlans: boolean;
    enableMeteredBilling: boolean;
    defaultCredits: number;
    creditPrice: number;
    creditPackages: Array<{
      credits: number;
      price: number;
      name: string;
      description: string;
      type: string;
      stripePriceId?: string; // Optional in config, injected from env vars
    }>;
    oneTimeCreditPackages: Array<{
      credits: number;
      price: number;
      name: string;
      description: string;
      type: string;
      stripePriceId?: string; // Optional in config, injected from env vars
    }>;
  };
  subscriptionPlans: Array<{
    id: string;
    name: string;
    price: number;
    currency: string;
    interval: string;
    features: string[];
    stripePriceId: string;
    popular: boolean;
  }>;
  navigation: {
    header: Array<{ name: string; href: string }>;
    userMenu: Array<{ name: string; href: string }>;
  };
  coreFeatures: Array<{
    title: string;
    description: string;
    icon: string;
  }>;
}

export const config: AppConfig = appConfig as AppConfig;

// Helper functions to get specific config values
export const getAppName = () => config.app.name;
export const getAppDescription = () => config.app.description;
export const getPrimaryColor = () => config.app.primaryColor;
export const getSubscriptionPlans = () => config.subscriptionPlans;

// Credit packages with dynamic Stripe price IDs from environment variables
export const getCreditPackages = (): Array<{
  credits: number;
  price: number;
  name: string;
  description: string;
  type: string;
  stripePriceId: string;
}> => {
  const packages = [...config.billing.creditPackages];

  // Map package names to environment variable price IDs
  const priceIdMap: Record<string, string> = {
    'Starter': env.STRIPE_PRICE_ID_STARTER,
    'Professional': env.STRIPE_PRICE_ID_PROFESSIONAL,
    'Premium': env.STRIPE_PRICE_ID_PREMIUM,
    'Enterprise': env.STRIPE_PRICE_ID_ENTERPRISE || 'price_enterprise_placeholder'
  };

  // Inject the environment-based price IDs
  return packages.map(pkg => ({
    ...pkg,
    stripePriceId: priceIdMap[pkg.name] || pkg.stripePriceId || 'price_placeholder'
  }));
};

export const getOneTimeCreditPackages = (): Array<{
  credits: number;
  price: number;
  name: string;
  description: string;
  type: string;
  stripePriceId: string;
}> => {
  const packages = [...config.billing.oneTimeCreditPackages];

  // Map credit amounts to environment variable price IDs
  const priceIdMap: Record<number, string> = {
    100: env.STRIPE_PRICE_ID_100_CREDITS,
    200: env.STRIPE_PRICE_ID_200_CREDITS,
    300: env.STRIPE_PRICE_ID_300_CREDITS,
    500: env.STRIPE_PRICE_ID_500_CREDITS
  };

  // Inject the environment-based price IDs
  return packages.map(pkg => ({
    ...pkg,
    stripePriceId: priceIdMap[pkg.credits] || pkg.stripePriceId || 'price_placeholder'
  }));
};

export const getBillingModel = () => config.billing.model;
export const getCurrency = () => config.billing.currency;
export const getNavigation = () => config.navigation;
export const getCoreFeatures = () => config.coreFeatures;

// Simplified feature helpers
export const isDocumentAnalysisEnabled = () => config.features.documentAnalysis.enabled;
export const getDocumentAnalysisCreditCost = () => config.features.documentAnalysis.creditCost;

// Environment variables with fallbacks for build time
export const env = {
  DATABASE_URL: process.env.DATABASE_URL || 'mysql://dummy:dummy@localhost:3306/dummy',
  NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'http://localhost:3000',
  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET || 'dummy-secret-for-build-time',
  STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY || 'pk_test_dummy_key_for_build',
  STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY || 'sk_test_dummy_key_for_build',
  STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET || 'whsec_dummy_secret_for_build',
  JWT_SECRET: process.env.JWT_SECRET || 'dummy-jwt-secret-for-build-time',
  NODE_ENV: process.env.NODE_ENV || 'development',
  DOCUMENT_ANALYSIS_API_ENDPOINT: process.env.DOCUMENT_ANALYSIS_API_ENDPOINT || 'https://api.docuchamp.ai/analyze',
  DOCUMENT_ANALYSIS_AUTH_SECRET: process.env.DOCUMENT_ANALYSIS_AUTH_SECRET || 'dummy-auth-secret-for-build',
  ENABLE_CREDIT_MANAGEMENT: process.env.ENABLE_CREDIT_MANAGEMENT === 'true',
  CREDIT_RENEWAL_CHECK_INTERVAL_HOURS: parseInt(process.env.CREDIT_RENEWAL_CHECK_INTERVAL_HOURS || '1'),
  // Document analysis configuration
  USE_DUMMY_ANALYSIS: process.env.USE_DUMMY_ANALYSIS === 'true',
  AZURE_FUNCTION_ENDPOINT: process.env.AZURE_FUNCTION_ENDPOINT || 'https://your-function-app.azurewebsites.net/api/analyze',
  AZURE_FUNCTION_KEY: process.env.AZURE_FUNCTION_KEY || 'dummy-function-key-for-build',
  // AZF_DOCUCHAMP configuration
  AZF_DOCUCHAMP_FUNCTION_ENDPOINT: process.env.AZF_DOCUCHAMP_FUNCTION_ENDPOINT || 'https://azf-docuchamp.azurewebsites.net/api/orchestrators/analyze_document_orchestrator',
  AZF_DOCUCHAMP_FUNCTION_KEY: process.env.AZF_DOCUCHAMP_FUNCTION_KEY || 'dummy-azf-key-for-build',
  WEBHOOK_ANALYSIS_COMPLETE_KEY: process.env.WEBHOOK_ANALYSIS_COMPLETE_KEY || 'dummy-webhook-key-for-build',
  WEBHOOK_SECRET: process.env.WEBHOOK_SECRET || 'dummy-webhook-secret-for-build',
  // Debug Tools Configuration
  ENABLE_DEBUG_TOOLS: process.env.NEXT_PUBLIC_ENABLE_DEBUG_TOOLS === 'true',
  // Google Analytics
  GA_MEASUREMENT_ID: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || '',
  // Stripe Price IDs
  STRIPE_PRICE_ID_STARTER: process.env.STRIPE_PRICE_ID_STARTER || 'price_starter_placeholder',
  STRIPE_PRICE_ID_PROFESSIONAL: process.env.STRIPE_PRICE_ID_PROFESSIONAL || 'price_professional_placeholder',
  STRIPE_PRICE_ID_PREMIUM: process.env.STRIPE_PRICE_ID_PREMIUM || 'price_premium_placeholder',
  STRIPE_PRICE_ID_ENTERPRISE: process.env.STRIPE_PRICE_ID_ENTERPRISE || 'price_enterprise_placeholder',
  STRIPE_PRICE_ID_100_CREDITS: process.env.STRIPE_PRICE_ID_100_CREDITS || 'price_100_credits_placeholder',
  STRIPE_PRICE_ID_200_CREDITS: process.env.STRIPE_PRICE_ID_200_CREDITS || 'price_200_credits_placeholder',
  STRIPE_PRICE_ID_300_CREDITS: process.env.STRIPE_PRICE_ID_300_CREDITS || 'price_300_credits_placeholder',
  STRIPE_PRICE_ID_500_CREDITS: process.env.STRIPE_PRICE_ID_500_CREDITS || 'price_500_credits_placeholder'
} as const;

export default config;
