import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { getUserById, updateUserStripeCustomerId, getActiveSubscription } from '@/lib/db'
import { createStripeCustomer, createCustomerPortalSession } from '@/lib/stripe-utils'
import { stripe } from '@/lib/stripe'
import { getCreditPackages } from '@/lib/config'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const userId = (session?.user as any)?.id
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { packageId } = await request.json()
    if (!packageId) {
      return NextResponse.json({ error: 'Package ID is required' }, { status: 400 })
    }

    const user = await getUserById(userId)
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Check if user already has an active subscription
    const activeSubscription = await getActiveSubscription(user.id)
    if (activeSubscription && user.stripeCustomerId) {
      // User has active subscription, redirect to customer portal
      const portalSession = await createCustomerPortalSession(
        user.stripeCustomerId,
        `${process.env.NEXTAUTH_URL}/billing`
      )
      return NextResponse.json({
        url: portalSession.url,
        type: 'portal'
      })
    }

    // Find the credit package
    const creditPackages = getCreditPackages()
    const selectedPackage = creditPackages.find((pkg, index) => index.toString() === packageId)

    if (!selectedPackage) {
      return NextResponse.json({ error: 'Invalid package' }, { status: 400 })
    }

    if (!selectedPackage.stripePriceId) {
      return NextResponse.json({ error: 'Package not configured for purchase' }, { status: 400 })
    }

    // Get or create Stripe customer
    let customerId: string

    // Check if user already has a Stripe customer ID
    if (user.stripeCustomerId) {
      customerId = user.stripeCustomerId
    } else {
      // Create new Stripe customer
      const customer = await createStripeCustomer(user.email, user.name || undefined)
      customerId = customer.id

      // Update customer metadata with user ID and store customer ID on user
      await stripe.customers.update(customerId, {
        metadata: {
          userId: user.id,
        },
      })

      // Store the customer ID on the user record
      await updateUserStripeCustomerId(user.id, customerId)
    }

    // Create checkout session for monthly subscription
    const checkoutSession = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: selectedPackage.stripePriceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXTAUTH_URL}/billing?success=true&type=subscription`,
      cancel_url: `${process.env.NEXTAUTH_URL}/pricing?canceled=true`,
      metadata: {
        type: 'monthly_credits',
        credits: selectedPackage.credits.toString(),
        packageName: selectedPackage.name,
        packageCredit: selectedPackage.credits.toString(),
      },
      subscription_data: {
        metadata: {
          type: 'monthly_credits',
          credits: selectedPackage.credits.toString(),
          packageName: selectedPackage.name,
          packageCredit: selectedPackage.credits.toString(),
        },
      },
    })

    return NextResponse.json({ url: checkoutSession.url })
  } catch (error) {
    console.error('Credit checkout error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
