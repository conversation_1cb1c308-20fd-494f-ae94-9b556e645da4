import { NextRequest, NextResponse } from 'next/server'
import { getUserByEmail } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')

    if (!email) {
      return NextResponse.json(
        { error: 'Email parameter is required' },
        { status: 400 }
      )
    }

    const user = await getUserByEmail(email)
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Return user without sensitive information
    return NextResponse.json(
      { 
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          emailVerified: user.emailVerified,
        }
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('❌ Get user by email error:', error)
    return NextResponse.json(
      { error: 'An error occurred while fetching user information.' },
      { status: 500 }
    )
  }
}
