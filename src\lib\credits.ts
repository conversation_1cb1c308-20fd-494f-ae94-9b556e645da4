import { 
  getUserById, 
  deductCreditsFromUser, 
  recordUsage, 
  getUsageByUser 
} from './db'

export interface CreditUsage {
  feature: string
  credits: number
  description?: string
  metadata?: any
}

export class InsufficientCreditsError extends Error {
  constructor(required: number, available: number) {
    super(`Insufficient credits. Required: ${required}, Available: ${available}`)
    this.name = 'InsufficientCreditsError'
  }
}

/**
 * Check if user has sufficient credits for an operation
 */
export async function checkCredits(userId: string, requiredCredits: number): Promise<boolean> {
  const user = await getUserById(userId)
  if (!user) {
    throw new Error('User not found')
  }
  
  return user.credits >= requiredCredits
}

/**
 * Consume credits for a specific feature/operation
 */
export async function consumeCredits(
  userId: string, 
  usage: CreditUsage
): Promise<{ success: boolean; remainingCredits: number }> {
  const user = await getUserById(userId)
  if (!user) {
    throw new Error('User not found')
  }

  if (user.credits < usage.credits) {
    throw new InsufficientCreditsError(usage.credits, user.credits)
  }

  // Deduct credits from user
  const updatedUser = await deductCreditsFromUser(userId, usage.credits)

  // Record the usage in UsageRecord (new schema)
  await recordUsage({
    userId,
    operationType: usage.feature || 'document_analysis',
    creditSpent: usage.credits,
    status: 'completed',
    metadata: usage.metadata
  })

  return {
    success: true,
    remainingCredits: updatedUser.credits,
  }
}

/**
 * Get user's credit balance
 */
export async function getCreditBalance(userId: string): Promise<number> {
  const user = await getUserById(userId)
  if (!user) {
    throw new Error('User not found')
  }
  
  return user.credits
}

/**
 * Get user's usage history
 */
export async function getUserUsageHistory(userId: string, limit = 50) {
  return getUsageByUser(userId, limit)
}

/**
 * Calculate credits required for different operations
 */
export const CREDIT_COSTS = {
  DOCUMENT_ANALYSIS: 1,
  STOCK_ANALYSIS: 3,
  PORTFOLIO_OPTIMIZATION: 10,
  MARKET_INSIGHTS: 2,
  ADVANCED_REPORT: 15,
  API_CALL: 1,
} as const

/**
 * Get credit cost for a specific feature
 */
export function getCreditCost(feature: keyof typeof CREDIT_COSTS): number {
  return CREDIT_COSTS[feature]
}

/**
 * Middleware function to check and consume credits before operation
 */
export async function withCredits<T>(
  userId: string,
  feature: keyof typeof CREDIT_COSTS,
  operation: () => Promise<T>,
  description?: string,
  metadata?: any
): Promise<T> {
  const requiredCredits = getCreditCost(feature)
  
  // Check if user has sufficient credits
  const hasCredits = await checkCredits(userId, requiredCredits)
  if (!hasCredits) {
    const currentBalance = await getCreditBalance(userId)
    throw new InsufficientCreditsError(requiredCredits, currentBalance)
  }

  try {
    // Execute the operation first
    const result = await operation()

    // If successful, consume the credits
    await consumeCredits(userId, {
      feature,
      credits: requiredCredits,
      description: description || `${feature} operation`,
      metadata,
    })

    return result
  } catch (error) {
    // If operation fails, don't consume credits
    throw error
  }
}

/**
 * Get usage statistics for a user
 */
export async function getUserUsageStats(userId: string, days = 30) {
  const usage = await getUserUsageHistory(userId, 1000) // Get more records for stats
  
  const cutoffDate = new Date()
  cutoffDate.setDate(cutoffDate.getDate() - days)
  
  const recentUsage = usage.filter(u => new Date(u.createdAt) >= cutoffDate)
  
  const stats = {
    totalCreditsUsed: recentUsage.reduce((sum, u) => sum + (u.creditSpent || 0), 0),
    totalOperations: recentUsage.length,
    averageCreditsPerDay: 0,
    featureBreakdown: {} as Record<string, { count: number; credits: number }>,
  }

  // Calculate feature breakdown by operation type
  const breakdown: Record<string, { count: number; credits: number }> = {}
  recentUsage.forEach(u => {
    const operationType = u.operationType || 'document_analysis'
    if (!breakdown[operationType]) {
      breakdown[operationType] = { count: 0, credits: 0 }
    }
    breakdown[operationType].count += 1
    breakdown[operationType].credits += u.creditSpent || 0
  })
  stats.featureBreakdown = breakdown
  
  // Calculate average credits per day
  if (days > 0) {
    stats.averageCreditsPerDay = stats.totalCreditsUsed / days
  }
  
  return stats
}
