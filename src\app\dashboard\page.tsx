'use client'

import { useSession } from 'next-auth/react'
import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { UsageChart } from '@/components/dashboard/usage-chart'
import { CreditCard, BarChart3, FileText } from 'lucide-react'

interface CreditData {
  balance: number
  stats: {
    totalCreditsUsed: number
    totalOperations: number
    averageCreditsPerDay: number
    featureBreakdown: Record<string, { count: number; credits: number }>
  }
}

export default function DashboardPage() {
  const { data: session } = useSession()
  const [creditData, setCreditData] = useState<CreditData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const userId = (session?.user as any)?.id
    if (userId) {
      fetchCreditData()
    }
  }, [session])

  const fetchCreditData = async () => {
    try {
      const response = await fetch('/api/credits/balance?stats=true')
      if (response.ok) {
        const data = await response.json()
        setCreditData(data)
      }
    } catch (error) {
      console.error('Error fetching credit data:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <ProtectedRoute>
      <div className="p-8 bg-gray-50 min-h-screen">
        <div className="max-w-7xl mx-auto">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {session?.user?.name || session?.user?.email}!
          </h1>
          <p className="text-gray-600 mt-2">
            Here&apos;s your AI-powered document analysis dashboard
          </p>
          {creditData?.balance === 10 && (
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-green-800 font-medium">
                🎉 Welcome! You&apos;ve received 10 free credits to get started.
              </p>
            </div>
          )}
        </div>

        {/* Credit Balance Card */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Available Credits</p>
                <p className="text-3xl font-bold text-purple-500">
                  {loading ? '...' : creditData?.balance || 0}
                </p>
              </div>
              <CreditCard className="w-8 h-8 text-purple-500" />
            </div>
            <div className="mt-4">
              <Link href="/pricing">
                <Button variant="outline" size="sm">Purchase Credits</Button>
              </Link>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Credits Used</p>
                <p className="text-2xl font-bold text-gray-900">
                  {loading ? '...' : creditData?.stats?.totalCreditsUsed || 0}
                </p>
              </div>
              <BarChart3 className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Operations</p>
                <p className="text-2xl font-bold text-gray-900">
                  {loading ? '...' : creditData?.stats?.totalOperations || 0}
                </p>
              </div>
              <FileText className="w-8 h-8 text-green-500" />
            </div>
          </div>
        </div>



        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <Link href="/features/document-analysis">
              <Button variant="outline" className="w-full justify-start">
                <FileText className="w-4 h-4 mr-2" />
                Analyze Document
              </Button>
            </Link>
            <Link href="/billing">
              <Button variant="outline" className="w-full justify-start">
                <CreditCard className="w-4 h-4 mr-2" />
                Manage Billing
              </Button>
            </Link>
            <Link href="/pricing">
              <Button variant="outline" className="w-full justify-start">
                <BarChart3 className="w-4 h-4 mr-2" />
                Upgrade Plan
              </Button>
            </Link>
          </div>
        </div>

        {/* Usage Analytics Chart */}
        <UsageChart />
        </div>
      </div>
    </ProtectedRoute>
  )
}
