import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { consumeCredits, InsufficientCreditsError } from '@/lib/credits'

export async function POST(request: NextRequest) {
  console.log('Credit deduct endpoint called')
  try {
    const session = await getServerSession(authOptions)
    const userId = (session?.user as any)?.id
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { credits, description } = await request.json()

    if (!credits || credits <= 0) {
      return NextResponse.json({ error: 'Invalid credit amount' }, { status: 400 })
    }

    // Use the credits library to consume credits
    const result = await consumeCredits(userId, {
      feature: 'API_CALL', // Use API_CALL as the feature type
      credits,
      description: description || 'Credit deduction',
      metadata: {
        type: 'manual_deduction',
        timestamp: new Date().toISOString()
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Credits deducted successfully',
      remainingCredits: result.remainingCredits,
      deductedAmount: credits
    })

  } catch (error: unknown) {
    console.error('Credit deduction error:', error)
    
    if (error instanceof InsufficientCreditsError) {
      return NextResponse.json(
        { error: 'Insufficient credits' },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to deduct credits' },
      { status: 500 }
    )
  }
}
