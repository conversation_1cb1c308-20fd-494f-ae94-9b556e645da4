import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { getUserById } from '@/lib/db'
import { createCustomerPortalSession } from '@/lib/stripe-utils'

export async function POST() {
  console.log('🏪 Billing portal route called')

  try {
    const session = await getServerSession(authOptions)
    const userId = (session?.user as any)?.id
    console.log('👤 Session:', userId ? 'Found' : 'Not found')

    if (!userId) {
      console.log('❌ No session found')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await getUserById(userId)
    console.log('👤 User:', user ? 'Found' : 'Not found')
    console.log('💳 Stripe Customer ID:', user?.stripeCustomerId || 'None')

    if (!user) {
      console.log('❌ User not found in database')
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Check if user has a Stripe customer ID
    if (!user.stripeCustomerId) {
      console.log('❌ No Stripe customer ID found')
      return NextResponse.json(
        { error: 'No billing information found. Please make a purchase first.' },
        { status: 404 }
      )
    }

    console.log('🔄 Creating portal session...')
    // Create customer portal session
    const portalSession = await createCustomerPortalSession(
      user.stripeCustomerId,
      `${process.env.NEXTAUTH_URL}/billing`
    )

    console.log('✅ Portal session created:', portalSession.url)
    return NextResponse.json({ url: portalSession.url })
  } catch (error) {
    console.error('❌ Portal session error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
