'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { User, Mail, Calendar, Settings } from 'lucide-react'
import { ChangePasswordForm } from '@/components/profile/change-password-form'

export default function ProfilePage() {
  const { data: session, update } = useSession()
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [isSSO, setIsSSO] = useState<boolean | null>(null) // null = loading, true = SSO, false = not SSO
  const [formData, setFormData] = useState({
    name: session?.user?.name || '',
    email: session?.user?.email || '',
  })

  // Check if user is SSO user
  useEffect(() => {
    const checkSSO = async () => {
      if ((session?.user as any)?.id) {
        try {
          const response = await fetch('/api/user/check-sso')
          if (response.ok) {
            const { isSSO: userIsSSO } = await response.json()
            setIsSSO(userIsSSO)
          }
        } catch (error) {
          console.error('Error checking SSO status:', error)
        }
      }
    }

    checkSSO()
  }, [(session?.user as any)?.id])

  const handleSave = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        await update({ name: formData.name })
        setIsEditing(false)
      } else {
        console.error('Failed to update profile')
      }
    } catch (error) {
      console.error('Error updating profile:', error)
    } finally {
      setLoading(false)
    }
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">Please sign in to view your profile.</p>
          <Button onClick={() => window.location.href = '/auth/signin'}>
            Sign In
          </Button>
        </div>
      </div>
    )
  }

  // Show loading state while checking SSO status
  if (isSSO === null) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Profile Settings</h1>
          <p className="text-gray-600 mt-2">Manage your account information and preferences</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
          {/* Profile Information */}
          <div className="h-full">
            <Card className="h-full flex flex-col">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="w-5 h-5 mr-2" />
                  Personal Information
                </CardTitle>
                <CardDescription>
                  Update your personal details and contact information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6 flex-1 flex flex-col">
                <div className="space-y-4 flex-1">
                  <div>
                    <Label htmlFor="name">Full Name</Label>
                    {isEditing ? (
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        placeholder="Enter your full name"
                      />
                    ) : (
                      <div className="mt-1 p-3 bg-gray-50 rounded-md">
                        {session.user?.name || 'Not provided'}
                      </div>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="email">Email Address</Label>
                    <div className="mt-1 p-3 bg-gray-50 rounded-md flex items-center">
                      <Mail className="w-4 h-4 mr-2 text-gray-400" />
                      {session.user?.email}
                    </div>
                    <p className="text-sm text-gray-500 mt-1">
                      Email cannot be changed. Contact support if needed.
                    </p>
                  </div>

                  <div>
                    <Label>Account Created</Label>
                    <div className="mt-1 p-3 bg-gray-50 rounded-md flex items-center">
                      <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                      {new Date().toLocaleDateString()} {/* You can replace this with actual creation date */}
                    </div>
                  </div>
                </div>

                <div className="flex space-x-4 mt-auto">
                  {isEditing ? (
                    <>
                      <Button onClick={handleSave} disabled={loading}>
                        {loading ? 'Saving...' : 'Save Changes'}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setIsEditing(false)
                          setFormData({
                            name: session?.user?.name || '',
                            email: session?.user?.email || '',
                          })
                        }}
                      >
                        Cancel
                      </Button>
                    </>
                  ) : (
                    <Button onClick={() => setIsEditing(true)}>
                      <Settings className="w-4 h-4 mr-2" />
                      Edit Profile
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Change Password - Only show for non-SSO users */}
          {isSSO === false && (
            <div className="h-full">
              <ChangePasswordForm userEmail={session.user?.email || ''} />
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => window.location.href = '/dashboard'}
            >
              Go to Dashboard
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => window.location.href = '/pricing'}
            >
              View Pricing Plans
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start text-red-600 hover:text-red-700"
              onClick={() => window.location.href = '/auth/signout'}
            >
              Sign Out
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
