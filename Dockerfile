# 1. Install dependencies and build the project
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Install dependencies
COPY package.json package-lock.json ./
COPY prisma ./prisma/
COPY config ./config
COPY . .

RUN npm install

# Add Prisma schema and run "prisma generate"
RUN npx prisma generate

# Build the Next.js app
RUN npm run build

# 2. Production image
FROM node:20-alpine AS runner

WORKDIR /app

ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_ENV=production

# Copy built output & prisma client
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/config ./config


# Expose port (default Next.js port)
EXPOSE 3000

# Start the app
CMD ["npm", "start"]