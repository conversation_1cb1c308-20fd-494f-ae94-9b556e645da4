import type { Metadata } from 'next'
import { generateMetadata } from '@/lib/seo-config'

export const metadata: Metadata = generateMetadata({
  title: 'Profile Settings',
  description: 'Manage your account information and preferences. Update your profile details and account settings.',
  keywords: [
    'DocuChampAI profile',
    'account settings',
    'user profile',
    'account management',
    'profile settings',
    'user preferences'
  ],
})

export default function ProfileLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
