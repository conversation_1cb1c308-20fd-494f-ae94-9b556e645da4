import { NextRequest, NextResponse } from 'next/server'
import { generateOTP, createVerificationToken, sendOTPEmail } from '@/lib/otp'
import { getUserById } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json()

    // Validate input
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Check if user exists
    const user = await getUserById(userId)
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if user is already verified
    if (user.emailVerified) {
      return NextResponse.json(
        { error: 'Email is already verified' },
        { status: 400 }
      )
    }

    // Generate new OTP and send email
    const otp = generateOTP()
    await createVerificationToken(user.id, otp, 'email_verification', 15)

    try {
      await sendOTPEmail(user.email, user.name || 'User', otp, 'verification')
      console.log(`🔄 OTP resent for user: ${user.email}`)
    } catch (emailError) {
      console.error('❌ Failed to resend verification email:', emailError)
      return NextResponse.json(
        { error: 'Failed to send verification email. Please try again or contact support if the problem persists.' },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { 
        message: 'Verification code sent! Please check your email.',
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('❌ Resend OTP error:', error)
    return NextResponse.json(
      { error: 'An error occurred while sending verification code. Please try again.' },
      { status: 500 }
    )
  }
}
