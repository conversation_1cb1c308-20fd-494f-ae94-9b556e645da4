'use client'

import { useCallback } from 'react'
import {
  trackEvent,
  trackAuth,
  trackDocumentAnalysis,
  trackBilling,
  trackNavigation,
  trackEngagement,
  setUserProperties,
  isGAEnabled,
} from '@/lib/analytics'

/**
 * Custom hook for analytics tracking
 * 
 * Provides a convenient interface for tracking various events
 * throughout the application.
 */
export function useAnalytics() {
  // Generic event tracking
  const track = useCallback((
    action: string,
    category: string,
    label?: string,
    value?: number
  ) => {
    trackEvent(action, category, label, value)
  }, [])

  // Authentication tracking
  const auth = {
    signUp: useCallback((method?: string) => trackAuth.signUp(method), []),
    signIn: useCallback((method?: string) => trackAuth.signIn(method), []),
    signOut: useCallback(() => trackAuth.signOut(), []),
  }

  // Document analysis tracking
  const documentAnalysis = {
    start: useCallback((fileType: string, fileSize: number) =>
      trackDocumentAnalysis.start(fileType, fileSize), []),
    complete: useCallback((fileType: string, processingTime: number) =>
      trackDocumentAnalysis.complete(fileType, processingTime), []),
    error: useCallback((fileType: string, errorType: string) =>
      trackDocumentAnalysis.error(fileType, errorType), []),
    download: useCallback((fileType: string) =>
      trackDocumentAnalysis.download(fileType), []),
  }

  // Billing and subscription tracking
  const billing = {
    subscriptionStart: useCallback((planName: string, amount: number) =>
      trackBilling.subscriptionStart(planName, amount), []),
    subscriptionCancel: useCallback((planName: string) =>
      trackBilling.subscriptionCancel(planName), []),
    creditPurchase: useCallback((amount: number, credits: number) =>
      trackBilling.creditPurchase(amount, credits), []),
  }

  // Navigation tracking
  const navigation = {
    pageView: useCallback((pageName: string) => trackNavigation.pageView(pageName), []),
    linkClick: useCallback((linkText: string, destination: string) =>
      trackNavigation.linkClick(linkText, destination), []),
    buttonClick: useCallback((buttonName: string, location: string) =>
      trackNavigation.buttonClick(buttonName, location), []),
  }

  // Engagement tracking
  const engagement = {
    featureUsage: useCallback((featureName: string) =>
      trackEngagement.featureUsage(featureName), []),
    timeOnPage: useCallback((pageName: string, timeInSeconds: number) =>
      trackEngagement.timeOnPage(pageName, timeInSeconds), []),
    scrollDepth: useCallback((percentage: number) =>
      trackEngagement.scrollDepth(percentage), []),
  }

  // User properties
  const setUser = useCallback((properties: Record<string, any>) => {
    setUserProperties(properties)
  }, [])

  // Check if analytics is enabled
  const isEnabled = useCallback(() => isGAEnabled(), [])

  return {
    track,
    auth,
    documentAnalysis,
    billing,
    navigation,
    engagement,
    setUser,
    isEnabled,
  }
}
