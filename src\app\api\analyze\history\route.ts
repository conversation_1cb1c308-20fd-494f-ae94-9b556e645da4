import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { getUsageByUserPaginated } from '@/lib/db'

// GET /api/analyze/history
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const userId = (session?.user as any)?.id

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status') || undefined
    const search = searchParams.get('search') || undefined
    const dateFrom = searchParams.get('dateFrom') ? new Date(searchParams.get('dateFrom')!) : undefined
    const dateTo = searchParams.get('dateTo') ? new Date(searchParams.get('dateTo')!) : undefined

    // Validate page and limit
    if (page < 1 || limit < 1 || limit > 100) {
      return NextResponse.json({
        error: 'Invalid pagination parameters. Page must be >= 1, limit must be 1-100'
      }, { status: 400 })
    }

    // Get user's usage records with pagination and filtering
    const result = await getUsageByUserPaginated(userId, {
      page,
      limit,
      status,
      search,
      dateFrom,
      dateTo
    })

    // Format the response
    const formattedRecords = result.records.map(record => ({
      operationId: record.operationId,
      operationType: record.operationType,
      status: record.status,
      success: record.status === 'completed',
      creditSpent: record.creditSpent,
      fileName: record.fileName,
      fileSize: record.fileSize,
      fileLink: record.fileLink,
      output_file_blob_sas_url: record.fileLink, // For compatibility
      createdAt: record.createdAt,
      updatedAt: record.updatedAt,
      completedAt: record.completedAt,
      metadata: record.metadata
    }))

    return NextResponse.json({
      success: true,
      records: formattedRecords,
      pagination: result.pagination,
      filters: {
        status,
        search,
        dateFrom: dateFrom?.toISOString(),
        dateTo: dateTo?.toISOString()
      }
    })

  } catch (error: any) {
    console.error('Error getting analysis history:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 })
  }
}
