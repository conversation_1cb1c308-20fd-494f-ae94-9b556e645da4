# AI Stock Selector Template

A comprehensive NextJS template for building AI-powered SaaS applications with subscription management, credit-based billing, and flexible feature configuration. Perfect for creating AI stock analysis tools, document processing platforms, or any AI-driven service.

## 🚀 Features

- **🔐 Complete Authentication System** - NextAuth.js with email/password and social login support
- **💳 Dual Billing Models** - Support for both subscription-based and credit-based billing via Stripe
- **⚡ AI Feature Framework** - Flexible template system for building AI-powered features
- **📊 Credit Management** - Built-in credit tracking, usage monitoring, and purchase flow
- **🎨 Customizable UI** - Modern, responsive design with Tailwind CSS and shadcn/ui components
- **📱 Mobile-First Design** - Fully responsive across all devices
- **🔧 Easy Configuration** - JSON-based configuration system for quick customization
- **📈 Analytics Ready** - Built-in support for tracking user behavior and feature usage
- **🛡️ Security First** - Rate limiting, input validation, and secure API endpoints
- **📚 Comprehensive Documentation** - Detailed guides for setup, customization, and deployment

## 🛠️ Tech Stack

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: MySQL (configurable)
- **Authentication**: NextAuth.js
- **Payments**: Stripe (subscriptions + one-time payments)
- **UI Components**: shadcn/ui, Radix UI
- **Deployment**: Vercel, Docker support
- **i18n**: Built-in support for multiple languages, using react language state

## 📋 Prerequisites

- Node.js 18+
- MySQL database
- Stripe account
- Git

## ⚡ Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd web-template
npm install
```

### 2. Environment Setup

```bash
cp .env.example .env
```

Fill in your environment variables:

```env
# Database
DATABASE_URL="mysql://username:password@localhost:3306/database_name"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret"

# Stripe
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# JWT
JWT_SECRET="your-jwt-secret"
```

### 3. Database Setup

```bash
npx prisma migrate dev
npx prisma generate
```

### 4. Configuration (Optional)

Run the interactive configuration setup:

```bash
node scripts/setup-config.js
```

### 5. Start Development Server

```bash
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see your application!

## 📖 Documentation

- [Configuration Guide](docs/CONFIGURATION.md) - Customize your app
- [Deployment Guide](docs/DEPLOYMENT.md) - Deploy to production
- [API Reference](docs/API.md) - API endpoints and usage
- [Feature Development](docs/FEATURES.md) - Build new AI features
- [Stripe Setup](docs/STRIPE.md) - Configure payments

## 🎯 Use Cases

This template is perfect for building:

- **AI Stock Analysis Platforms** - Market analysis, stock recommendations
- **Document Processing Services** - AI-powered document analysis and insights
- **Investment Research Tools** - Portfolio optimization, risk assessment
- **Financial Advisory Platforms** - Automated investment advice
- **Market Intelligence Services** - Trend analysis, sentiment tracking
- **Any AI-Powered SaaS** - Flexible framework for various AI applications

## 🔧 Configuration

The application uses a simplified JSON-based configuration system:

- `config/app.config.json` - App branding, features, navigation, and pricing plans

## 💡 Key Features Explained

### Dual Billing System
- **Subscriptions**: Monthly/yearly plans with Stripe subscriptions

### AI Feature Framework
- **Template System**: Reusable components for AI features
- **Credit Integration**: Automatic credit deduction for feature usage
- **Usage Tracking**: Monitor feature usage and performance
- **Error Handling**: Graceful handling of AI service failures

### User Management
- **Authentication**: Secure login with NextAuth.js
- **Profile Management**: User settings and preferences
- **Billing Dashboard**: Subscription and credit management
- **Usage Analytics**: Track user engagement and feature usage

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy!

### Docker

```bash
docker build -t ai-stock-selector .
docker run -p 3000:3000 ai-stock-selector
```

### Traditional Hosting

See [Deployment Guide](docs/DEPLOYMENT.md) for detailed instructions.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/example)
- 📖 Documentation: [Full documentation](docs/)
- 🐛 Issues: [GitHub Issues](https://github.com/example/issues)

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - The React framework
- [Stripe](https://stripe.com/) - Payment processing
- [Prisma](https://prisma.io/) - Database toolkit
- [Tailwind CSS](https://tailwindcss.com/) - CSS framework
- [shadcn/ui](https://ui.shadcn.com/) - UI components
