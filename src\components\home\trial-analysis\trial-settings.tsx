'use client'

import React from 'react'
import { ChevronDown, Lock } from 'lucide-react'
import { useTranslation } from '@/hooks/use-translation'

interface TrialSettingsProps {
  language: string
  reportLength: string
  onLanguageChange: (language: string) => void
  onReportLengthChange: (length: string) => void
  onSignUpClick: () => void // Keeping for compatibility, but not using
}

export function TrialSettings({
  language,
  reportLength,
  onLanguageChange,
  onReportLengthChange,
  onSignUpClick: _onSignUpClick // Unused but kept for compatibility
}: TrialSettingsProps) {
  const { t } = useTranslation('trial')

  return (
    <div className="space-y-6">
      {/* Analysis Type - Fixed for Trial */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          {t('settings.analysisType')}
        </h3>
        <div className="bg-white border-2 border-purple-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-gray-900">{t('settings.analysisTypeDescription')}</h4>
              <p className="text-sm text-gray-600">{t('settings.analysisTypeSubtext')}</p>
            </div>
            <div className="px-3 py-1 bg-purple-100 text-purple-700 text-xs font-medium rounded-full">
              {t('settings.freeTrial')}
            </div>
          </div>
        </div>
        
        {/* Locked Options */}
        <div className="mt-3 space-y-2">
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 opacity-60">
            <div className="flex items-center space-x-2">
              <Lock className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-600">{t('settings.lockedAnalysisTypes.financeReport')}</span>
            </div>
          </div>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 opacity-60">
            <div className="flex items-center space-x-2">
              <Lock className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-600">{t('settings.lockedAnalysisTypes.legalDocument')}</span>
            </div>
          </div>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 opacity-60">
            <div className="flex items-center space-x-2">
              <Lock className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-600">{t('settings.lockedAnalysisTypes.educationalContent')}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Report Settings */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Report Language */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('settings.reportLanguage')}
          </label>
          <div className="relative">
            <select
              value={language}
              onChange={(e) => onLanguageChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white transition-colors"
            >
              <option value="English">{t('settings.languages.English')}</option>
              <option value="Chinese">{t('settings.languages.Chinese')}</option>
              <option value="Spanish">{t('settings.languages.Spanish')}</option>
              <option value="French">{t('settings.languages.French')}</option>
              <option value="German">{t('settings.languages.German')}</option>
              <option value="Japanese">{t('settings.languages.Japanese')}</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
          </div>
        </div>

        {/* Report Length */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('settings.reportLength')}
          </label>
          <div className="relative">
            <select
              value={reportLength}
              onChange={(e) => onReportLengthChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white transition-colors"
            >
              <option value="short">{t('settings.lengths.Short')}</option>
              <option value="medium">{t('settings.lengths.Medium')}</option>
              <option value="long">{t('settings.lengths.Long')}</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
          </div>
        </div>
      </div>

      {/* Trial Limitations Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
            <svg className="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <h4 className="text-sm font-medium text-blue-900 mb-1">{t('settings.trialLimitations.title')}</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>{t('settings.trialLimitations.items.0')}</li>
              <li>{t('settings.trialLimitations.items.1')}</li>
              <li>{t('settings.trialLimitations.items.2')}</li>
              <li>{t('settings.trialLimitations.items.3')}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
